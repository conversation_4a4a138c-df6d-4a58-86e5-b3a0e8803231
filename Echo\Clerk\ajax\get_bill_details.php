<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    echo "Unauthorized access";
    exit();
}

$bill_id = $_POST['bill_id'] ?? 0;
$clerk_hotel_id = $_SESSION['clerk_hotel_id'];

// Get bill details
$bill_query = "
    SELECT b.*,
           r.ReservationID, r.StartDate, r.EndDate, r.NumberOfGuests, r.RateType,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.Last<PERSON>ame, 'Guest')) as CustomerName,
           c.Email, c.Phone,
           rm.RoomNumber,
           rt.TypeName as RoomType, rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate,
           ci.CheckInDateTime, ci.ActualCheckOutDateTime,
           h.Name as HotelName
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    LEFT JOIN CheckIns ci ON b.CheckInID = ci.CheckInID
    LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    JOIN Hotels h ON r.HotelID = h.HotelID
    WHERE b.BillingID = ? AND r.HotelID = ?
";
$stmt = $conn->prepare($bill_query);
$stmt->bind_param("ii", $bill_id, $clerk_hotel_id);
$stmt->execute();
$bill = $stmt->get_result()->fetch_assoc();

if (!$bill) {
    echo "Bill not found";
    exit();
}

// Get additional charges
$charges_query = "
    SELECT * FROM AdditionalCharges
    WHERE BillingID = ?
    ORDER BY ChargeDate DESC
";
$stmt = $conn->prepare($charges_query);
$stmt->bind_param("i", $bill_id);
$stmt->execute();
$charges_result = $stmt->get_result();

// Calculate nights stayed
$checkin_date = new DateTime($bill['CheckInDateTime'] ?: $bill['StartDate']);
$checkout_date = new DateTime($bill['ActualCheckOutDateTime'] ?: $bill['EndDate']);
$nights = $checkin_date->diff($checkout_date)->days;
if ($nights == 0) $nights = 1;

// Calculate room charges
$room_charges = 0;
if ($bill['RateType'] === 'Weekly') {
    $weeks = ceil($nights / 7);
    $room_charges = $weeks * $bill['WeeklyRate'];
} elseif ($bill['RateType'] === 'Monthly') {
    $months = ceil($nights / 30);
    $room_charges = $months * $bill['MonthlyRate'];
} else {
    $room_charges = $nights * $bill['DailyRate'];
}

$additional_charges_total = 0;

// Check if this is a no-show charge
$is_no_show = $bill['PaymentMethod'] === 'No-Show Charge';
?>

<div class="bill-details">
    <?php if ($is_no_show): ?>
    <div class="alert alert-danger mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-2"><i class="fa fa-exclamation-triangle"></i> No-Show Charge</h5>
                <p class="mb-0">This is a no-show billing record. Customer failed to check-in for their reservation and has been charged the standard no-show fee of LKR 1,500.00 as per hotel policy.</p>
            </div>
            <div class="col-md-4 text-center">
                <div class="bg-white rounded p-3">
                    <h6 class="text-danger mb-1">No-Show Fee</h6>
                    <h4 class="text-danger mb-0">LKR 1,500.00</h4>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="text-center mb-4">
        <h4><?php echo htmlspecialchars($bill['HotelName']); ?></h4>
        <h5>Guest Bill Statement</h5>
        <p>Bill ID: <?php echo $bill['BillingID']; ?> | Date: <?php echo date('F d, Y', strtotime($bill['BillingDateTime'])); ?></p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h6>Guest Information</h6>
            <p>
                <strong>Name:</strong> <?php echo htmlspecialchars($bill['CustomerName']); ?><br>
                <?php if ($bill['Email']): ?>
                    <strong>Email:</strong> <?php echo htmlspecialchars($bill['Email']); ?><br>
                <?php endif; ?>
                <?php if ($bill['Phone']): ?>
                    <strong>Phone:</strong> <?php echo htmlspecialchars($bill['Phone']); ?><br>
                <?php endif; ?>
                <strong>Guests:</strong> <?php echo $bill['NumberOfGuests']; ?>
            </p>
        </div>
        <div class="col-md-6">
            <h6>Stay Information</h6>
            <p>
                <strong>Room:</strong> <?php echo $bill['RoomNumber']; ?> (<?php echo htmlspecialchars($bill['RoomType']); ?>)<br>
                <strong>Check-in:</strong> <?php echo date('M d, Y H:i', strtotime($bill['CheckInDateTime'] ?: $bill['StartDate'])); ?><br>
                <strong>Check-out:</strong> <?php echo date('M d, Y H:i', strtotime($bill['ActualCheckOutDateTime'] ?: $bill['EndDate'])); ?><br>
                <strong>Nights:</strong> <?php echo $nights; ?><br>
                <strong>Rate Type:</strong> <?php echo $bill['RateType']; ?>
            </p>
        </div>
    </div>

    <hr>

    <h6>Charges Breakdown</h6>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Description</th>
                <th>Quantity</th>
                <th>Rate</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Room Charges (<?php echo htmlspecialchars($bill['RoomType']); ?>)</td>
                <td><?php echo $nights; ?> nights</td>
                <td><?php echo format_currency($bill['DailyRate']); ?>/night</td>
                <td class="text-right"><?php echo format_currency($room_charges); ?></td>
            </tr>

            <?php if ($charges_result->num_rows > 0): ?>
                <?php while ($charge = $charges_result->fetch_assoc()): ?>
                    <?php $additional_charges_total += $charge['Amount']; ?>
                    <tr>
                        <td><?php echo htmlspecialchars($charge['ChargeType']); ?></td>
                        <td>1</td>
                        <td><?php echo htmlspecialchars($charge['Description']); ?></td>
                        <td class="text-right"><?php echo format_currency($charge['Amount']); ?></td>
                    </tr>
                <?php endwhile; ?>
            <?php endif; ?>

            <tr class="table-active">
                <td colspan="3"><strong>Total Amount</strong></td>
                <td class="text-right"><strong><?php echo format_currency($bill['TotalAmount']); ?></strong></td>
            </tr>
        </tbody>
    </table>

    <div class="row mt-3">
        <div class="col-md-6">
            <p>
                <strong>Payment Method:</strong> <?php echo htmlspecialchars($bill['PaymentMethod']); ?><br>
                <strong>Payment Status:</strong>
                <span class="badge badge-<?php
                    echo $bill['PaymentStatus'] === 'Paid' ? 'success' :
                        ($bill['PaymentStatus'] === 'No-Show' ? 'danger' : 'warning');
                ?>">
                    <?php echo htmlspecialchars($bill['PaymentStatus']); ?>
                </span>
            </p>
        </div>
        <div class="col-md-6 text-right">
            <p class="text-muted">
                <small>
                    Thank you for staying with us!<br>
                    We hope you enjoyed your stay.
                </small>
            </p>
        </div>
    </div>

    <?php
    // Show customer payment history if customer exists and is not a walk-in
    if ($bill['CustomerName'] && $bill['CustomerName'] !== 'Walk-in Guest') {
        $customer_query = "
            SELECT b.BillingID, b.TotalAmount, b.PaymentMethod, b.PaymentStatus, b.BillingDateTime,
                   r.ReservationID, h.Name as HotelName
            FROM Billing b
            JOIN Reservations r ON b.ReservationID = r.ReservationID
            JOIN Hotels h ON r.HotelID = h.HotelID
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            WHERE CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) = ?
            AND b.BillingID != ?
            AND r.HotelID = ?
            ORDER BY b.BillingDateTime DESC
            LIMIT 5
        ";
        $customer_stmt = $conn->prepare($customer_query);
        $customer_stmt->bind_param("sii", $bill['CustomerName'], $bill_id, $clerk_hotel_id);
        $customer_stmt->execute();
        $customer_result = $customer_stmt->get_result();

        if ($customer_result->num_rows > 0) {
            echo '
            <hr>
            <h6><i class="fa fa-history"></i> Customer Payment History</h6>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Bill ID</th>
                            <th>Reservation</th>
                            <th>Hotel</th>
                            <th>Amount</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>';

            while ($history = $customer_result->fetch_assoc()) {
                $status_badge = $history['PaymentStatus'] === 'Paid' ? 'success' :
                               ($history['PaymentStatus'] === 'No-Show' ? 'danger' : 'warning');
                $method_badge = $history['PaymentMethod'] === 'No-Show Charge' ? 'danger' : 'info';

                echo '<tr>
                        <td><span class="badge badge-light">#' . $history['BillingID'] . '</span></td>
                        <td><span class="badge badge-light">#' . $history['ReservationID'] . '</span></td>
                        <td>' . htmlspecialchars($history['HotelName']) . '</td>
                        <td>' . format_currency($history['TotalAmount']) . '</td>
                        <td><span class="badge badge-' . $method_badge . '">' . htmlspecialchars($history['PaymentMethod']) . '</span></td>
                        <td><span class="badge badge-' . $status_badge . '">' . htmlspecialchars($history['PaymentStatus']) . '</span></td>
                        <td>' . date('M j, Y', strtotime($history['BillingDateTime'])) . '</td>
                      </tr>';
            }

            echo '</tbody>
                </table>
            </div>';
        }
    }
    ?>
</div>
