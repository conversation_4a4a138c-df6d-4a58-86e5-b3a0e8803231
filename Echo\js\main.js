/*  ---------------------------------------------------
    Template Name: Echo
    Description: Echo Hotel Html Template
    Author: Colorlib
    Author URI: https://colorlib.com
    Version: 1.0
    Created: Colorlib
---------------------------------------------------------  */

'use strict';

(function ($) {

    /*------------------
        Preloader
    --------------------*/
    $(window).on('load', function () {
        $(".loader").fadeOut();
        $("#preloder").delay(200).fadeOut("slow");
    });

    /*------------------
        Background Set
    --------------------*/
    $('.set-bg').each(function () {
        var bg = $(this).data('setbg');
        $(this).css('background-image', 'url(' + bg + ')');
    });

    //Offcanvas Menu
    $(".canvas-open").on('click', function () {
        $(".offcanvas-menu-wrapper").addClass("show-offcanvas-menu-wrapper");
        $(".offcanvas-menu-overlay").addClass("active");
    });

    $(".canvas-close, .offcanvas-menu-overlay").on('click', function () {
        $(".offcanvas-menu-wrapper").removeClass("show-offcanvas-menu-wrapper");
        $(".offcanvas-menu-overlay").removeClass("active");
    });

    // Search model
    $('.search-switch').on('click', function () {
        $('.search-model').fadeIn(400);
    });

    $('.search-close-switch').on('click', function () {
        $('.search-model').fadeOut(400, function () {
            $('#search-input').val('');
        });
    });

    /*------------------
		Navigation
	--------------------*/
    $(".mobile-menu").slicknav({
        prependTo: '#mobile-menu-wrap',
        allowParentLinks: true
    });

    /*------------------
        Hero Slider
    --------------------*/
   $(".hero-slider").owlCarousel({
        loop: true,
        margin: 0,
        items: 1,
        dots: true,
        animateOut: 'fadeOut',
        animateIn: 'fadeIn',
        smartSpeed: 1200,
        autoHeight: false,
        autoplay: true,
        mouseDrag: false
    });

    /*------------------------
		Testimonial Slider
    ----------------------- */
    $(".testimonial-slider").owlCarousel({
        items: 1,
        dots: false,
        autoplay: true,
        loop: true,
        smartSpeed: 1200,
        nav: true,
        navText: ["<i class='arrow_left'></i>", "<i class='arrow_right'></i>"]
    });

    /*------------------
        Magnific Popup
    --------------------*/
    $('.video-popup').magnificPopup({
        type: 'iframe'
    });

    /*------------------
		Date Picker - Removed to use native HTML5 date inputs
	--------------------*/
    // $(".date-input").datepicker({
    //     minDate: 0,
    //     dateFormat: 'dd MM, yy'
    // });

    /*------------------
		Nice Select
	--------------------*/
    $("select").niceSelect();

    // Sign Up Form Handling
    document.addEventListener('DOMContentLoaded', function() {
        const signupForm = document.getElementById('signupForm');
        if (signupForm) {
            signupForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form values
                const fullName = document.getElementById('fullName').value;
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const phone = document.getElementById('phone').value;

                // Validate passwords match
                if (password !== confirmPassword) {
                    alert('Passwords do not match!');
                    return;
                }

                // Create user object
                const userData = {
                    fullName,
                    email,
                    password,
                    phone
                };

                // Here you would typically send this data to your backend server
                // For now, we'll just store it in localStorage as a demo
                try {
                    // Get existing users or initialize empty array
                    const users = JSON.parse(localStorage.getItem('users')) || [];

                    // Check if email already exists
                    if (users.some(user => user.email === email)) {
                        alert('Email already registered!');
                        return;
                    }

                    // Add new user
                    users.push(userData);
                    localStorage.setItem('users', JSON.stringify(users));

                    // Clear form
                    signupForm.reset();

                    // Show success message
                    alert('Registration successful! You can now log in.');

                    // Optionally redirect to login page or home page
                    // window.location.href = 'index.html';
                } catch (error) {
                    console.error('Error saving user data:', error);
                    alert('An error occurred during registration. Please try again.');
                }
            });
        }
    });

})(jQuery);