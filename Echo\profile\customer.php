<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in as customer
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: ../auth/login.php?type=customer');
    exit();
}

$customer_id = $_SESSION['user_id'];
$message = '';
$message_type = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $phone = sanitize_input($_POST['phone']);

    $update_query = "UPDATE Customers SET FirstName = ?, LastName = ?, Phone = ?, UpdatedAt = NOW() WHERE CustomerID = ?";
    $stmt = $conn->prepare($update_query);
    $stmt->bind_param("sssi", $first_name, $last_name, $phone, $customer_id);

    if ($stmt->execute()) {
        $message = 'Profile updated successfully!';
        $message_type = 'success';
        $_SESSION['user_name'] = $first_name . ' ' . $last_name;
    } else {
        $message = 'Error updating profile.';
        $message_type = 'error';
    }
}

// Handle payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['pay_bill'])) {
    $billing_id = intval($_POST['billing_id']);
    $payment_method = sanitize_input($_POST['payment_method']);
    $card_number = sanitize_input($_POST['card_number'] ?? '');
    $expiry_date = sanitize_input($_POST['expiry_date'] ?? '');
    $cvv = sanitize_input($_POST['cvv'] ?? '');
    $card_name = sanitize_input($_POST['card_name'] ?? '');

    try {
        // Verify the billing record belongs to this customer
        $verify_query = "
            SELECT b.BillingID, b.TotalAmount, b.PaymentStatus, r.CustomerID
            FROM Billing b
            JOIN Reservations r ON b.ReservationID = r.ReservationID
            WHERE b.BillingID = ? AND r.CustomerID = ?
        ";
        $stmt = $conn->prepare($verify_query);
        $stmt->bind_param("ii", $billing_id, $customer_id);
        $stmt->execute();
        $bill_result = $stmt->get_result();

        if ($bill_result->num_rows === 0) {
            throw new Exception('Invalid billing record');
        }

        $bill = $bill_result->fetch_assoc();

        if ($bill['PaymentStatus'] === 'Paid') {
            throw new Exception('This bill has already been paid');
        }

        // Simulate payment processing (in real implementation, integrate with payment gateway)
        $payment_successful = true; // Simulate successful payment

        if ($payment_successful) {
            // Update billing record
            $update_query = "
                UPDATE Billing
                SET PaymentStatus = 'Paid',
                    PaymentMethod = ?,
                    BillingDateTime = NOW(),
                    UpdatedAt = NOW()
                WHERE BillingID = ?
            ";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("si", $payment_method, $billing_id);

            if ($stmt->execute()) {
                $message = 'Payment processed successfully! Thank you for your payment.';
                $message_type = 'success';
            } else {
                throw new Exception('Failed to update payment status');
            }
        } else {
            throw new Exception('Payment processing failed. Please try again.');
        }

    } catch (Exception $e) {
        $message = 'Payment failed: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get customer information
$customer_query = "SELECT * FROM Customers WHERE CustomerID = ?";
$stmt = $conn->prepare($customer_query);
$stmt->bind_param("i", $customer_id);
$stmt->execute();
$customer_result = $stmt->get_result();
$customer = $customer_result->fetch_assoc();

// Get customer reservations
$reservations_query = "
    SELECT r.*, h.Name as HotelName, rt.TypeName as RoomType, rt.DailyRate
    FROM Reservations r
    JOIN Hotels h ON r.HotelID = h.HotelID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.CustomerID = ?
    ORDER BY r.CreatedAt DESC
";
$stmt = $conn->prepare($reservations_query);
$stmt->bind_param("i", $customer_id);
$stmt->execute();
$reservations_result = $stmt->get_result();

// Get customer billing records
$billing_query = "
    SELECT b.*, r.ReservationID, r.StartDate, r.EndDate, r.Status as ReservationStatus,
           h.Name as HotelName, rt.TypeName as RoomType
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    JOIN Hotels h ON r.HotelID = h.HotelID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.CustomerID = ?
    ORDER BY b.BillingDateTime DESC
";
$stmt = $conn->prepare($billing_query);
$stmt->bind_param("i", $customer_id);
$stmt->execute();
$billing_result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Profile - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .profile-container {
            padding: 50px 0;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .profile-header {
            background: linear-gradient(135deg,rgb(87, 173, 173) 0%,rgb(62, 88, 159) 50%,rgb(13, 140, 104) 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
        }

        .profile-content {
            background: white;
            padding: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .nav-tabs .nav-link {
            color: #666;
        }

        .nav-tabs .nav-link.active {
            background-color:rgb(97, 165, 190);
            color: white;

        }

        .btn-primary {
            background:rgb(87, 154, 157);

        }

        .btn-primary:hover {
            background:rgb(83, 153, 183);

        }

        .reservation-card {
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-confirmed { background: #d4edda; color: #155724; }
        .status-canceled { background: #f8d7da; color: #721c24; }
        .status-noshow { background: #fff3cd; color: #856404; }

        .billing-summary-card {
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            transition: transform 0.2s;
        }

        .billing-summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <img src="../img/logo.png" alt="Echo Hotels" height="40">
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="../index.php">Home</a>
                <a class="nav-link" href="customer.php">Profile</a>
                <a class="nav-link" href="../auth/logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="profile-container">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="profile-header">
                        <div class="row align-items-center">
                            <div class="col-md-12">
                                <h2><i class="fa fa-user"></i> Welcome, <?php echo htmlspecialchars($customer['FirstName'] . ' ' . $customer['LastName']); ?></h2>
                                <p class="mb-0" style="color: white;">Customer since <?php echo date('F Y', strtotime($customer['CreatedAt'])); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="profile-content">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?>" role="alert">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <!-- Tabs -->
                        <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="profile-tab" data-toggle="tab" href="#profile" role="tab">Profile Information</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="reservations-tab" data-toggle="tab" href="#reservations" role="tab">My Reservations</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="billing-tab" data-toggle="tab" href="#billing" role="tab">Billing & Payments</a>
                            </li>
                        </ul>

                        <div class="tab-content" id="profileTabsContent">
                            <!-- Profile Information Tab -->
                            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                                <div class="row mt-4">
                                    <div class="col-md-8">
                                        <form method="POST">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>First Name</label>
                                                        <input type="text" name="first_name" class="form-control" value="<?php echo htmlspecialchars($customer['FirstName']); ?>" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Last Name</label>
                                                        <input type="text" name="last_name" class="form-control" value="<?php echo htmlspecialchars($customer['LastName']); ?>" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label>Email</label>
                                                <input type="email" class="form-control" value="<?php echo htmlspecialchars($customer['Email']); ?>" readonly>
                                                <small class="form-text text-muted">Email cannot be changed</small>
                                            </div>

                                            <div class="form-group">
                                                <label>Phone</label>
                                                <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($customer['Phone']); ?>" required>
                                            </div>

                                            <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                                        </form>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <i class="fa fa-user-circle fa-5x text-muted mb-3"></i>
                                                <h5><?php echo htmlspecialchars($customer['FirstName'] . ' ' . $customer['LastName']); ?></h5>
                                                <p class="text-muted"><?php echo htmlspecialchars($customer['Email']); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Reservations Tab -->
                            <div class="tab-pane fade" id="reservations" role="tabpanel">
                                <div class="mt-4">
                                    <h4>Reservation History</h4>

                                    <?php if ($reservations_result->num_rows > 0): ?>
                                        <?php while ($reservation = $reservations_result->fetch_assoc()): ?>
                                        <div class="reservation-card">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h5><?php echo htmlspecialchars($reservation['HotelName']); ?></h5>
                                                    <p><strong>Room Type:</strong> <?php echo htmlspecialchars($reservation['RoomType']); ?></p>
                                                    <p><strong>Dates:</strong> <?php echo date('M j, Y', strtotime($reservation['StartDate'])); ?> - <?php echo date('M j, Y', strtotime($reservation['EndDate'])); ?></p>
                                                    <p><strong>Rate:</strong> <?php echo format_currency($reservation['DailyRate']); ?> per night</p>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <span class="status-badge status-<?php echo strtolower($reservation['Status']); ?>">
                                                        <?php echo htmlspecialchars($reservation['Status']); ?>
                                                    </span>
                                                    <br><br>
                                                    <?php if ($reservation['Status'] === 'Confirmed' && strtotime($reservation['StartDate']) > time()): ?>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="cancelReservation(<?php echo $reservation['ReservationID']; ?>)">Cancel</button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="text-center py-5">
                                            <i class="fa fa-calendar fa-3x text-muted mb-3"></i>
                                            <h5>No reservations yet</h5>
                                            <p class="text-muted">Start planning your next stay with us!</p>
                                            <a href="../index.php" class="btn btn-primary">Browse Hotels</a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Billing & Payments Tab -->
                            <div class="tab-pane fade" id="billing" role="tabpanel">
                                <div class="mt-4">
                                    <h4>Billing & Payment History</h4>

                                    <?php
                                    // Check for reservation fees (no-show charges) first
                                    $billing_result->data_seek(0); // Reset result pointer
                                    $reservation_fees = [];
                                    $other_bills = [];
                                    $total_reservation_fees = 0;

                                    while ($bill = $billing_result->fetch_assoc()) {
                                        if ($bill['PaymentMethod'] === 'No-Show Charge') {
                                            $reservation_fees[] = $bill;
                                            if ($bill['PaymentStatus'] !== 'Paid') {
                                                $total_reservation_fees += $bill['TotalAmount'];
                                            }
                                        } else {
                                            $other_bills[] = $bill;
                                        }
                                    }
                                    ?>

                                    <?php if (!empty($reservation_fees)): ?>
                                    <!-- Reservation Fees Alert Section -->
                                    <div class="alert alert-warning border-left-warning shadow-sm mb-4" style="border-left: 4px solid #f6c23e;">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <h5 class="alert-heading mb-2">
                                                    <i class="fa fa-exclamation-triangle text-warning"></i>
                                                    Outstanding Reservation Fees
                                                </h5>
                                                <p class="mb-1">
                                                    You have <strong><?php echo count($reservation_fees); ?> reservation fee(s)</strong>
                                                    for reservations where you didn't show up.
                                                </p>
                                                <?php if ($total_reservation_fees > 0): ?>
                                                <p class="mb-0">
                                                    <strong>Total Outstanding:</strong>
                                                    <span class="text-danger h5"><?php echo format_currency($total_reservation_fees); ?></span>
                                                </p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-4 text-right">
                                                <?php if ($total_reservation_fees > 0): ?>
                                                <button class="btn btn-warning btn-lg" onclick="scrollToReservationFees()">
                                                    <i class="fa fa-credit-card"></i> Pay Fees
                                                </button>
                                                <?php else: ?>
                                                <span class="badge badge-success badge-lg">All Paid</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Reservation Fees Table -->
                                    <div class="card shadow-sm mb-4" id="reservationFeesSection">
                                        <div class="card-header bg-warning text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-exclamation-circle"></i>
                                                Reservation Fees (No-Show Charges)
                                            </h5>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="table-responsive">
                                                <table class="table table-striped mb-0">
                                                    <thead class="thead-light">
                                                        <tr>
                                                            <th>Fee ID</th>
                                                            <th>Reservation Details</th>
                                                            <th>Hotel</th>
                                                            <th>Fee Amount</th>
                                                            <th>Status</th>
                                                            <th>Date Charged</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($reservation_fees as $fee): ?>
                                                        <tr class="<?php echo $fee['PaymentStatus'] !== 'Paid' ? 'table-warning' : ''; ?>">
                                                            <td>
                                                                <strong>#<?php echo $fee['BillingID']; ?></strong>
                                                                <br><small class="text-muted">Reservation Fee</small>
                                                            </td>
                                                            <td>
                                                                <strong>Reservation #<?php echo $fee['ReservationID']; ?></strong><br>
                                                                <small class="text-muted">
                                                                    Check-in: <?php echo date('M j, Y', strtotime($fee['StartDate'])); ?><br>
                                                                    Check-out: <?php echo date('M j, Y', strtotime($fee['EndDate'])); ?>
                                                                </small>
                                                            </td>
                                                            <td>
                                                                <?php echo htmlspecialchars($fee['HotelName']); ?><br>
                                                                <small class="text-muted"><?php echo htmlspecialchars($fee['RoomType']); ?></small>
                                                            </td>
                                                            <td>
                                                                <strong class="text-danger h5"><?php echo format_currency($fee['TotalAmount']); ?></strong>
                                                                <br><small class="text-muted">No-Show Fee</small>
                                                            </td>
                                                            <td>
                                                                <span class="badge badge-<?php echo $fee['PaymentStatus'] === 'Paid' ? 'success' : 'danger'; ?> badge-lg">
                                                                    <?php echo $fee['PaymentStatus'] === 'Paid' ? 'PAID' : 'UNPAID'; ?>
                                                                </span>
                                                            </td>
                                                            <td><?php echo date('M j, Y', strtotime($fee['BillingDateTime'])); ?></td>
                                                            <td>
                                                                <?php if ($fee['PaymentStatus'] !== 'Paid'): ?>
                                                                    <button class="btn btn-danger btn-sm" onclick="showPaymentModal(<?php echo $fee['BillingID']; ?>, <?php echo $fee['TotalAmount']; ?>, 'Reservation Fee')">
                                                                        <i class="fa fa-credit-card"></i> Pay Fee
                                                                    </button>
                                                                <?php else: ?>
                                                                    <span class="text-success"><i class="fa fa-check-circle"></i> Paid</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-light">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <small class="text-muted">
                                                        <i class="fa fa-info-circle"></i>
                                                        Reservation fees are charged when you don't show up for confirmed reservations.
                                                        The fee is LKR 1,500.00 per missed reservation.
                                                    </small>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <?php if ($total_reservation_fees > 0): ?>
                                                    <strong>Total Outstanding: <span class="text-danger"><?php echo format_currency($total_reservation_fees); ?></span></strong>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($billing_result->num_rows > 0): ?>
                                    <!-- Regular Billing History -->
                                    <div class="card shadow-sm">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0">
                                                <i class="fa fa-credit-card"></i>
                                                Complete Billing History
                                            </h5>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead class="thead-light">
                                                        <tr>
                                                            <th>Bill ID</th>
                                                            <th>Reservation</th>
                                                            <th>Hotel</th>
                                                            <th>Amount</th>
                                                            <th>Type</th>
                                                            <th>Status</th>
                                                            <th>Date</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php
                                                        // Reset and show all bills
                                                        $billing_result->data_seek(0);
                                                        while ($bill = $billing_result->fetch_assoc()):
                                                            $is_reservation_fee = ($bill['PaymentMethod'] === 'No-Show Charge');
                                                        ?>
                                                        <tr class="<?php echo $is_reservation_fee ? 'table-warning' : ''; ?>">
                                                            <td>
                                                                #<?php echo $bill['BillingID']; ?>
                                                                <?php if ($is_reservation_fee): ?>
                                                                    <br><span class="badge badge-warning badge-sm">Fee</span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <strong>#<?php echo $bill['ReservationID']; ?></strong><br>
                                                                <small class="text-muted">
                                                                    <?php echo date('M j, Y', strtotime($bill['StartDate'])); ?> -
                                                                    <?php echo date('M j, Y', strtotime($bill['EndDate'])); ?>
                                                                </small>
                                                            </td>
                                                            <td>
                                                                <?php echo htmlspecialchars($bill['HotelName']); ?><br>
                                                                <small class="text-muted"><?php echo htmlspecialchars($bill['RoomType']); ?></small>
                                                            </td>
                                                            <td>
                                                                <strong class="<?php echo $is_reservation_fee ? 'text-danger' : 'text-success'; ?>">
                                                                    <?php echo format_currency($bill['TotalAmount']); ?>
                                                                </strong>
                                                                <?php if ($is_reservation_fee): ?>
                                                                    <br><small class="text-danger"><strong>Reservation Fee</strong></small>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <?php if ($is_reservation_fee): ?>
                                                                    <span class="badge badge-danger">No-Show Fee</span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-info"><?php echo htmlspecialchars($bill['PaymentMethod']); ?></span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <span class="badge badge-<?php echo $bill['PaymentStatus'] === 'Paid' ? 'success' : ($bill['PaymentStatus'] === 'No-Show' ? 'danger' : 'warning'); ?>">
                                                                    <?php echo htmlspecialchars($bill['PaymentStatus']); ?>
                                                                </span>
                                                            </td>
                                                            <td><?php echo date('M j, Y', strtotime($bill['BillingDateTime'])); ?></td>
                                                            <td>
                                                                <?php if ($bill['PaymentStatus'] !== 'Paid'): ?>
                                                                    <button class="btn btn-sm <?php echo $is_reservation_fee ? 'btn-danger' : 'btn-success'; ?>"
                                                                            onclick="showPaymentModal(<?php echo $bill['BillingID']; ?>, <?php echo $bill['TotalAmount']; ?>, '<?php echo $is_reservation_fee ? 'Reservation Fee' : htmlspecialchars($bill['PaymentMethod'], ENT_QUOTES); ?>')">
                                                                        <i class="fa fa-credit-card"></i>
                                                                        <?php echo $is_reservation_fee ? 'Pay Fee' : 'Pay Now'; ?>
                                                                    </button>
                                                                <?php else: ?>
                                                                    <span class="text-success"><i class="fa fa-check-circle"></i> Paid</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                        <?php endwhile; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                        <?php
                                        // Calculate summary statistics
                                        $billing_result->data_seek(0); // Reset result pointer
                                        $total_amount = 0;
                                        $pending_amount = 0;
                                        $reservation_fees_total = 0;
                                        $reservation_fees_unpaid = 0;
                                        $regular_bills_total = 0;

                                        while ($bill = $billing_result->fetch_assoc()) {
                                            $total_amount += $bill['TotalAmount'];

                                            if ($bill['PaymentStatus'] === 'Pending' || $bill['PaymentStatus'] === 'No-Show') {
                                                $pending_amount += $bill['TotalAmount'];
                                            }

                                            if ($bill['PaymentMethod'] === 'No-Show Charge') {
                                                $reservation_fees_total += $bill['TotalAmount'];
                                                if ($bill['PaymentStatus'] !== 'Paid') {
                                                    $reservation_fees_unpaid += $bill['TotalAmount'];
                                                }
                                            } else {
                                                $regular_bills_total += $bill['TotalAmount'];
                                            }
                                        }
                                        ?>

                                        <div class="row mt-4">
                                            <div class="col-md-3">
                                                <div class="card billing-summary-card text-center border-primary">
                                                    <div class="card-body">
                                                        <h5 class="card-title text-primary"><?php echo format_currency($total_amount); ?></h5>
                                                        <p class="card-text">Total Billed</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card billing-summary-card text-center border-warning">
                                                    <div class="card-body">
                                                        <h5 class="card-title text-warning"><?php echo format_currency($pending_amount); ?></h5>
                                                        <p class="card-text">Outstanding Amount</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card billing-summary-card text-center border-danger">
                                                    <div class="card-body">
                                                        <h5 class="card-title text-danger"><?php echo format_currency($reservation_fees_unpaid); ?></h5>
                                                        <p class="card-text">Unpaid Reservation Fees</p>
                                                        <?php if ($reservation_fees_unpaid > 0): ?>
                                                        <small class="text-muted">(<?php echo count($reservation_fees); ?> fee(s))</small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card billing-summary-card text-center border-success">
                                                    <div class="card-body">
                                                        <h5 class="card-title text-success"><?php echo format_currency($regular_bills_total); ?></h5>
                                                        <p class="card-text">Regular Bills</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    <?php else: ?>
                                        <div class="text-center py-5">
                                            <i class="fa fa-credit-card fa-3x text-muted mb-3"></i>
                                            <h5>No billing records</h5>
                                            <p class="text-muted">You have no billing history at this time.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fa fa-credit-card"></i> Pay Bill</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST" id="paymentForm">
                    <div class="modal-body">
                        <div id="reservationFeeAlert" class="alert alert-warning" style="display: none;">
                            <h6><i class="fa fa-exclamation-triangle"></i> Reservation Fee Payment</h6>
                            <p class="mb-0">This is a reservation fee charged because you didn't show up for your confirmed reservation. The fee is LKR 1,500.00 as per our no-show policy.</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6>Bill Information</h6>
                                <p><strong>Bill ID:</strong> #<span id="modalBillId"></span></p>
                                <p><strong>Amount:</strong> <span id="modalAmount" class="h5"></span></p>
                                <p><strong>Type:</strong> <span id="modalCurrentMethod"></span></p>
                            </div>
                            <div class="col-md-6">
                                <h6>Payment Method</h6>
                                <div class="form-group">
                                    <label>
                                        <input type="radio" name="payment_method" value="Credit Card" checked>
                                        <i class="fa fa-credit-card"></i> Credit Card
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="radio" name="payment_method" value="Cash">
                                        <i class="fa fa-money"></i> Cash (Pay at Hotel)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div id="creditCardSection">
                            <hr>
                            <h6>Credit Card Details</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Card Number</label>
                                        <input type="text" class="form-control" name="card_number" placeholder="1234 5678 9012 3456" maxlength="19">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Name on Card</label>
                                        <input type="text" class="form-control" name="card_name" placeholder="John Doe">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Expiry Date</label>
                                        <input type="text" class="form-control" name="expiry_date" placeholder="MM/YY" maxlength="5">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>CVV</label>
                                        <input type="text" class="form-control" name="cvv" placeholder="123" maxlength="4">
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fa fa-shield"></i> Your payment information is secure and encrypted.
                            </div>
                        </div>

                        <input type="hidden" name="billing_id" id="hiddenBillingId">
                        <input type="hidden" name="pay_bill" value="1">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fa fa-credit-card"></i> Process Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        function cancelReservation(reservationId) {
            if (confirm('Are you sure you want to cancel this reservation?')) {
                // Implement cancellation logic
                window.location.href = 'cancel_reservation.php?id=' + reservationId;
            }
        }

        function showPaymentModal(billingId, amount, currentMethod) {
            $('#modalBillId').text(billingId);
            $('#modalAmount').text('LKR ' + parseFloat(amount).toLocaleString('en-US', {minimumFractionDigits: 2}));
            $('#modalCurrentMethod').text(currentMethod);
            $('#hiddenBillingId').val(billingId);

            // Check if this is a reservation fee
            if (currentMethod === 'Reservation Fee' || currentMethod === 'No-Show Charge') {
                $('#reservationFeeAlert').show();
                $('#modalAmount').removeClass('text-success').addClass('text-danger');
                $('.modal-title').html('<i class="fa fa-exclamation-triangle text-warning"></i> Pay Reservation Fee');
            } else {
                $('#reservationFeeAlert').hide();
                $('#modalAmount').removeClass('text-danger').addClass('text-success');
                $('.modal-title').html('<i class="fa fa-credit-card"></i> Pay Bill');
            }

            $('#paymentModal').modal('show');
        }

        function scrollToReservationFees() {
            $('html, body').animate({
                scrollTop: $('#reservationFeesSection').offset().top - 100
            }, 800);
        }

        $(document).ready(function() {
            // Handle payment method selection
            $('input[name="payment_method"]').change(function() {
                if ($(this).val() === 'Credit Card') {
                    $('#creditCardSection').show();
                    $('#creditCardSection input').prop('required', true);
                } else {
                    $('#creditCardSection').hide();
                    $('#creditCardSection input').prop('required', false);
                }
            });

            // Format card number input
            $('input[name="card_number"]').on('input', function() {
                let value = $(this).val().replace(/\s/g, '');
                let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
                $(this).val(formattedValue);
            });

            // Format expiry date input
            $('input[name="expiry_date"]').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                $(this).val(value);
            });

            // Validate form submission
            $('#paymentForm').submit(function(e) {
                const paymentMethod = $('input[name="payment_method"]:checked').val();

                if (paymentMethod === 'Credit Card') {
                    const cardNumber = $('input[name="card_number"]').val().replace(/\s/g, '');
                    const cardName = $('input[name="card_name"]').val();
                    const expiryDate = $('input[name="expiry_date"]').val();
                    const cvv = $('input[name="cvv"]').val();

                    if (!cardNumber || cardNumber.length < 13 || !cardName || !expiryDate || !cvv) {
                        e.preventDefault();
                        alert('Please fill in all credit card details.');
                        return false;
                    }
                }

                // Show loading state
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Processing...');
                return true;
            });
        });
    </script>

    <style>
        .border-left-warning {
            border-left: 4px solid #f6c23e !important;
        }

        .billing-summary-card {
            transition: transform 0.2s;
        }

        .billing-summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .table-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        .badge-lg {
            font-size: 0.9em;
            padding: 0.5em 0.8em;
        }

        .alert-warning {
            border: 1px solid #f6c23e;
            background-color: #fff3cd;
        }

        #reservationFeesSection {
            border: 2px solid #f6c23e;
        }

        .card-header.bg-warning {
            background-color: #f6c23e !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
    </style>
</body>
</html>
