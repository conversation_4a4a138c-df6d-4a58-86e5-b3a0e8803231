<?php
// Database connection file for includes directory
// This file provides a consistent database connection for all PHP files

// Include the main database configuration
require_once(__DIR__ . '/../config/db.php');

// Additional helper functions can be added here if needed
// The main database connection ($conn) is available from config/db.php

// Function to check database connection status
function check_db_connection() {
    global $conn;
    if ($conn->connect_error) {
        die("Database connection failed: " . $conn->connect_error);
    }
    return true;
}

// Function to close database connection
function close_db_connection() {
    global $conn;
    if ($conn) {
        $conn->close();
    }
}
?>
