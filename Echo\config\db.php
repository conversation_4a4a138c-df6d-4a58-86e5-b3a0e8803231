<?php
// Database configuration
$db_host = 'localhost';
$db_name = 'hotelmanagementsystem';
$db_user = 'root';  // Default XAMPP MySQL username
$db_pass = '';      // Default XAMPP MySQL password has no password

// Create connection
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set charset to utf8mb4
$conn->set_charset("utf8mb4");

// Function to sanitize input
function sanitize_input($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $conn->real_escape_string($data);
}

// Function to format currency
function format_currency($amount) {
    return 'LKR ' . number_format($amount, 2);
}

// Function to check if a reservation should be auto-cancelled
function check_reservation_auto_cancel($reservation_id) {
    global $conn;

    $query = "
        SELECT r.*,
               TIMESTAMPDIFF(HOUR, r.CreatedAt, NOW()) as hours_since_creation,
               r.CreditCardDetails IS NULL as no_credit_card
        FROM Reservations r
        WHERE r.ReservationID = ?
        AND r.Status = 'Confirmed'
    ";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $reservation_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $reservation = $result->fetch_assoc();

    if ($reservation && $reservation['no_credit_card'] && $reservation['hours_since_creation'] >= 12) {
        // Auto-cancel reservation
        $update = "UPDATE Reservations SET Status = 'Canceled' WHERE ReservationID = ?";
        $stmt = $conn->prepare($update);
        $stmt->bind_param("i", $reservation_id);
        $stmt->execute();
        return true;
    }

    return false;
}

// Function to handle no-show billing
function handle_no_show_billing() {
    global $conn;

    // Get all reservations that are no-shows (past check-in date with no check-in record)
    $query = "
        SELECT r.*, rt.DailyRate
        FROM Reservations r
        JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        WHERE r.Status = 'Confirmed'
        AND r.StartDate < CURDATE()
        AND NOT EXISTS (
            SELECT 1 FROM CheckIns c
            WHERE c.ReservationID = r.ReservationID
        )
    ";

    $result = $conn->query($query);
    while ($reservation = $result->fetch_assoc()) {
        // Create billing record for no-show
        $billing_query = "
            INSERT INTO Billing (
                ReservationID,
                TotalAmount,
                PaymentMethod,
                PaymentStatus,
                BillingDateTime
            ) VALUES (?, ?, 'CreditCard', 'Pending', NOW())
        ";

        $stmt = $conn->prepare($billing_query);
        $total_amount = 1500.00; // Fixed no-show charge as per hotel policy
        $stmt->bind_param("id", $reservation['ReservationID'], $total_amount);
        $stmt->execute();

        // Update reservation status to NoShow
        $update = "UPDATE Reservations SET Status = 'NoShow' WHERE ReservationID = ?";
        $stmt = $conn->prepare($update);
        $stmt->bind_param("i", $reservation['ReservationID']);
        $stmt->execute();
    }
}

// Function to get occupancy report
function get_occupancy_report($date = null) {
    global $conn;

    if (!$date) {
        $date = date('Y-m-d', strtotime('-1 day')); // Default to yesterday
    }

    $query = "
        SELECT
            h.Name as HotelName,
            COUNT(DISTINCT r.RoomID) as TotalRooms,
            COUNT(DISTINCT CASE WHEN c.CheckInID IS NOT NULL THEN r.RoomID END) as OccupiedRooms,
            SUM(CASE WHEN b.BillingID IS NOT NULL THEN b.TotalAmount ELSE 0 END) as Revenue
        FROM Hotels h
        LEFT JOIN Rooms r ON h.HotelID = r.HotelID
        LEFT JOIN CheckIns c ON r.RoomID = c.RoomID
            AND DATE(c.CheckInDateTime) <= ?
            AND (c.ActualCheckOutDateTime IS NULL OR DATE(c.ActualCheckOutDateTime) > ?)
        LEFT JOIN Billing b ON c.CheckInID = b.CheckInID
            AND DATE(b.BillingDateTime) = ?
        GROUP BY h.HotelID, h.Name
    ";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("sss", $date, $date, $date);
    $stmt->execute();
    return $stmt->get_result();
}

// Schedule tasks to run at 7 PM daily
$current_hour = date('G');
if ($current_hour == 19) { // 7 PM
    // Auto-cancel reservations without credit card details
    $res_query = "SELECT ReservationID FROM Reservations WHERE Status = 'Confirmed'";
    $res_result = $conn->query($res_query);
    while ($row = $res_result->fetch_assoc()) {
        check_reservation_auto_cancel($row['ReservationID']);
    }

    // Handle no-show billing and generate occupancy report
    handle_no_show_billing();
    $occupancy_report = get_occupancy_report();

    // You might want to email this report to managers
    // Implementation of email sending would go here
}
?>