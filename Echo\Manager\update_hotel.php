<?php
session_start();
require_once('../config/db.php');

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get form data
$hotel_id = isset($_POST['hotel_id']) ? intval($_POST['hotel_id']) : 0;
$hotel_name = isset($_POST['hotel_name']) ? trim($_POST['hotel_name']) : '';
$hotel_location = isset($_POST['hotel_location']) ? trim($_POST['hotel_location']) : '';

// Validate input
if ($hotel_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid hotel ID']);
    exit();
}

if (empty($hotel_name)) {
    echo json_encode(['success' => false, 'message' => 'Hotel name is required']);
    exit();
}

if (empty($hotel_location)) {
    echo json_encode(['success' => false, 'message' => 'Hotel location is required']);
    exit();
}

try {
    // Check if hotel exists
    $check_query = "SELECT HotelID FROM Hotels WHERE HotelID = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $hotel_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Hotel not found']);
        exit();
    }
    
    // Check if another hotel with the same name exists (excluding current hotel)
    $name_check_query = "SELECT HotelID FROM Hotels WHERE Name = ? AND HotelID != ?";
    $name_check_stmt = $conn->prepare($name_check_query);
    $name_check_stmt->bind_param("si", $hotel_name, $hotel_id);
    $name_check_stmt->execute();
    $name_result = $name_check_stmt->get_result();
    
    if ($name_result->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'A hotel with this name already exists']);
        exit();
    }
    
    // Update hotel
    $update_query = "UPDATE Hotels SET Name = ?, Location = ? WHERE HotelID = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssi", $hotel_name, $hotel_location, $hotel_id);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Hotel updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update hotel']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>
