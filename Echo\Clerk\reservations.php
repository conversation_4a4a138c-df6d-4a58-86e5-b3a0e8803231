<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';

// Handle reservation Management
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['modify_reservation'])) {
        $reservation_id = $_POST['reservation_id'];
        $new_checkout_date = $_POST['new_checkout_date'];
        
        try {
            $update_query = "
                UPDATE Reservations 
                SET EndDate = ?, UpdatedAt = NOW() 
                WHERE ReservationID = ? AND HotelID = ?
            ";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("sii", $new_checkout_date, $reservation_id, $clerk_hotel_id);
            
            if ($stmt->execute()) {
                // Also update check-in expected checkout date if exists
                $update_checkin_query = "
                    UPDATE CheckIns ci
                    JOIN Reservations r ON ci.ReservationID = r.ReservationID
                    SET ci.ExpectedCheckOutDate = ?
                    WHERE r.ReservationID = ? AND r.HotelID = ?
                ";
                $stmt = $conn->prepare($update_checkin_query);
                $stmt->bind_param("sii", $new_checkout_date, $reservation_id, $clerk_hotel_id);
                $stmt->execute();
                
                $message = 'Reservation checkout date updated successfully!';
                $message_type = 'success';
            } else {
                throw new Exception('Failed to update reservation');
            }
            
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $message_type = 'error';
        }
    }
    
    if (isset($_POST['cancel_reservation'])) {
        $reservation_id = $_POST['reservation_id'];
        $cancellation_reason = $_POST['cancellation_reason'];
        
        try {
            $conn->begin_transaction();
            
            // Update reservation status
            $cancel_query = "
                UPDATE Reservations 
                SET Status = 'Canceled', CancellationReason = ?, UpdatedAt = NOW() 
                WHERE ReservationID = ? AND HotelID = ? AND Status = 'Confirmed'
            ";
            $stmt = $conn->prepare($cancel_query);
            $stmt->bind_param("sii", $cancellation_reason, $reservation_id, $clerk_hotel_id);
            
            if ($stmt->execute() && $stmt->affected_rows > 0) {
                // If there's a check-in, we need to handle checkout
                $checkin_query = "
                    SELECT CheckInID, RoomID 
                    FROM CheckIns ci 
                    JOIN Reservations r ON ci.ReservationID = r.ReservationID 
                    WHERE r.ReservationID = ? AND ci.ActualCheckOutDateTime IS NULL
                ";
                $stmt = $conn->prepare($checkin_query);
                $stmt->bind_param("i", $reservation_id);
                $stmt->execute();
                $checkin_result = $stmt->get_result()->fetch_assoc();
                
                if ($checkin_result) {
                    // Update check-in record
                    $checkout_query = "
                        UPDATE CheckIns 
                        SET ActualCheckOutDateTime = NOW(), UpdatedAt = NOW() 
                        WHERE CheckInID = ?
                    ";
                    $stmt = $conn->prepare($checkout_query);
                    $stmt->bind_param("i", $checkin_result['CheckInID']);
                    $stmt->execute();
                    
                    // Free up the room
                    $room_query = "UPDATE Rooms SET Status = 'Available' WHERE RoomID = ?";
                    $stmt = $conn->prepare($room_query);
                    $stmt->bind_param("i", $checkin_result['RoomID']);
                    $stmt->execute();
                }
                
                $conn->commit();
                $message = 'Reservation cancelled successfully!';
                $message_type = 'success';
            } else {
                throw new Exception('Reservation not found or cannot be cancelled');
            }
            
        } catch (Exception $e) {
            $conn->rollback();
            $message = 'Error: ' . $e->getMessage();
            $message_type = 'error';
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$date_filter = $_GET['date'] ?? '';
$search = $_GET['search'] ?? '';

// Build query with filters
$where_conditions = ["r.HotelID = ?"];
$params = [$clerk_hotel_id];
$param_types = "i";

if ($status_filter) {
    $where_conditions[] = "r.Status = ?";
    $params[] = $status_filter;
    $param_types .= "s";
}

if ($date_filter) {
    $where_conditions[] = "r.StartDate = ?";
    $params[] = $date_filter;
    $param_types .= "s";
}

if ($search) {
    $where_conditions[] = "(CONCAT(COALESCE(c.FirstName, ''), ' ', COALESCE(c.LastName, '')) LIKE ? OR r.ReservationID LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= "ss";
}

$where_clause = implode(" AND ", $where_conditions);

// Get reservations with filters
$reservations_query = "
    SELECT r.*, 
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           c.Email, c.Phone,
           rt.TypeName as RoomType,
           ci.CheckInDateTime, ci.ActualCheckOutDateTime, ci.RoomID,
           rm.RoomNumber,
           tc.CompanyName
    FROM Reservations r
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    LEFT JOIN CheckIns ci ON r.ReservationID = ci.ReservationID
    LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
    LEFT JOIN TravelCompanies tc ON r.CompanyID = tc.CompanyID
    WHERE $where_clause
    ORDER BY r.StartDate DESC, r.CreatedAt DESC
    LIMIT 50
";

$stmt = $conn->prepare($reservations_query);
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$reservations_result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservations Management - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="header">
                <h1>Reservations Management</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>
            
            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-3">
                    <div class="card-body">
                        <form method="GET" class="row">
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    <option value="Confirmed" <?php echo $status_filter === 'Confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                    <option value="CheckedIn" <?php echo $status_filter === 'CheckedIn' ? 'selected' : ''; ?>>Checked In</option>
                                    <option value="CheckedOut" <?php echo $status_filter === 'CheckedOut' ? 'selected' : ''; ?>>Checked Out</option>
                                    <option value="Canceled" <?php echo $status_filter === 'Canceled' ? 'selected' : ''; ?>>Cancelled</option>
                                    <option value="NoShow" <?php echo $status_filter === 'NoShow' ? 'selected' : ''; ?>>No Show</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($date_filter); ?>" placeholder="Check-in Date">
                            </div>
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by guest name or reservation ID">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary btn-block">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Reservations Table -->
                <div class="card">
                    <div class="card-header">
                        <h3>Reservations</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Guest/Company</th>
                                        <th>Contact</th>
                                        <th>Room Type</th>
                                        <th>Room #</th>
                                        <th>Check-in</th>
                                        <th>Check-out</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($reservations_result->num_rows > 0): ?>
                                        <?php while ($reservation = $reservations_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $reservation['ReservationID']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($reservation['CustomerName']); ?>
                                                <?php if ($reservation['CompanyName']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($reservation['CompanyName']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($reservation['Email']): ?>
                                                    <?php echo htmlspecialchars($reservation['Email']); ?><br>
                                                <?php endif; ?>
                                                <?php if ($reservation['Phone']): ?>
                                                    <?php echo htmlspecialchars($reservation['Phone']); ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($reservation['RoomType']); ?></td>
                                            <td><?php echo $reservation['RoomNumber'] ?? '-'; ?></td>
                                            <td>
                                                <?php echo $reservation['StartDate']; ?>
                                                <?php if ($reservation['CheckInDateTime']): ?>
                                                    <br><small class="text-success">Actual: <?php echo date('M d, H:i', strtotime($reservation['CheckInDateTime'])); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo $reservation['EndDate']; ?>
                                                <?php if ($reservation['ActualCheckOutDateTime']): ?>
                                                    <br><small class="text-info">Actual: <?php echo date('M d, H:i', strtotime($reservation['ActualCheckOutDateTime'])); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = 'secondary';
                                                if ($reservation['Status'] === 'Confirmed') $status_class = 'primary';
                                                elseif ($reservation['Status'] === 'CheckedIn') $status_class = 'success';
                                                elseif ($reservation['Status'] === 'CheckedOut') $status_class = 'info';
                                                elseif ($reservation['Status'] === 'Canceled') $status_class = 'danger';
                                                elseif ($reservation['Status'] === 'NoShow') $status_class = 'warning';
                                                ?>
                                                <span class="badge badge-<?php echo $status_class; ?>">
                                                    <?php echo $reservation['Status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <?php if ($reservation['Status'] === 'Confirmed'): ?>
                                                        <a href="checkin.php?reservation_id=<?php echo $reservation['ReservationID']; ?>" 
                                                           class="btn btn-primary btn-sm">Check-In</a>
                                                    <?php elseif ($reservation['Status'] === 'CheckedIn'): ?>
                                                        <a href="checkout.php?reservation_id=<?php echo $reservation['ReservationID']; ?>" 
                                                           class="btn btn-success btn-sm">Check-Out</a>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (in_array($reservation['Status'], ['Confirmed', 'CheckedIn'])): ?>
                                                        <button class="btn btn-warning btn-sm" 
                                                                onclick="modifyReservation(<?php echo $reservation['ReservationID']; ?>, 
                                                                                        '<?php echo $reservation['EndDate']; ?>')">
                                                            Modify
                                                        </button>
                                                        <button class="btn btn-danger btn-sm" 
                                                                onclick="cancelReservation(<?php echo $reservation['ReservationID']; ?>, 
                                                                                         '<?php echo htmlspecialchars($reservation['CustomerName']); ?>')">
                                                            Cancel
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center">No reservations found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modify Reservation Modal -->
    <div class="modal fade" id="modifyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Modify Reservation</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="modify_reservation" value="1">
                        <input type="hidden" id="modify_reservation_id" name="reservation_id">
                        
                        <div class="form-group">
                            <label>New Check-out Date *</label>
                            <input type="date" id="new_checkout_date" name="new_checkout_date" class="form-control" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Update Reservation</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cancel Reservation Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cancel Reservation</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="cancel_reservation" value="1">
                        <input type="hidden" id="cancel_reservation_id" name="reservation_id">
                        
                        <div class="alert alert-warning">
                            <strong>Warning:</strong> You are about to cancel the reservation for <span id="cancel_guest_name"></span>.
                            This action cannot be undone.
                        </div>
                        
                        <div class="form-group">
                            <label>Cancellation Reason *</label>
                            <textarea name="cancellation_reason" class="form-control" rows="3" required 
                                      placeholder="Please provide a reason for cancellation..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Keep Reservation</button>
                        <button type="submit" class="btn btn-danger">Cancel Reservation</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        function modifyReservation(reservationId, currentCheckout) {
            document.getElementById('modify_reservation_id').value = reservationId;
            document.getElementById('new_checkout_date').value = currentCheckout;
            $('#modifyModal').modal('show');
        }
        
        function cancelReservation(reservationId, guestName) {
            document.getElementById('cancel_reservation_id').value = reservationId;
            document.getElementById('cancel_guest_name').textContent = guestName;
            $('#cancelModal').modal('show');
        }
    </script>
</body>
</html>
