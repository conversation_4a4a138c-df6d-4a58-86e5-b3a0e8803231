<?php
session_start();
require_once('../config/db.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'User not logged in']);
    exit();
}

$user_id = $_SESSION['user_id'];
$user_type = $_SESSION['user_type'];

// Get form data
$hotel_id = (int)($_POST['hotel_id'] ?? 0);
$room_type_id = (int)($_POST['room_type_id'] ?? 0);
$check_in = $_POST['check_in'] ?? '';
$check_out = $_POST['check_out'] ?? '';
$guests = $_POST['guests'] ?? '';
$booking_type = $_POST['booking_type'] ?? 'guest';
$rate_type = $_POST['rate_type'] ?? 'daily';
$total_amount = (float)($_POST['total_amount'] ?? 0);
$credit_card_details = $_POST['credit_card_details'] ?? '';
$payment_method = $_POST['payment_method'] ?? 'with-card';

// Travel agent specific fields
$rooms_count = (int)($_POST['rooms_count'] ?? 1);
$nights = (int)($_POST['nights'] ?? 1);
$discount_rate = (int)($_POST['discount_rate'] ?? 0);
$company_name = $_POST['company_name'] ?? '';
$billing_info = $_POST['billing_info'] ?? '';

// Validation
if ($hotel_id === 0 || $room_type_id === 0 || empty($check_in) || empty($check_out)) {
    echo json_encode(['error' => 'Missing required fields']);
    exit();
}

// Validate user type matches booking type
if (($booking_type === 'agent' && $user_type !== 'agent') ||
    ($booking_type === 'guest' && $user_type !== 'customer')) {
    echo json_encode(['error' => 'User type does not match booking type']);
    exit();
}

// Calculate nights
$check_in_date = new DateTime($check_in);
$check_out_date = new DateTime($check_out);
$calculated_nights = $check_in_date->diff($check_out_date)->days;

// For travel agents, use the nights from form if provided, otherwise use calculated
$nights_to_use = ($booking_type === 'agent' && $nights > 0) ? $nights : $calculated_nights;

// Get room type information for rate calculation
$room_type_query = "SELECT * FROM roomtypes WHERE RoomTypeID = ?";
$stmt = $conn->prepare($room_type_query);
$stmt->bind_param("i", $room_type_id);
$stmt->execute();
$room_type_result = $stmt->get_result();
$room_type_data = $room_type_result->fetch_assoc();

if (!$room_type_data) {
    echo json_encode(['error' => 'Room type not found']);
    exit();
}

// Calculate the correct rate based on rate type
$rate_per_unit = 0;
switch ($rate_type) {
    case 'weekly':
        $rate_per_unit = $room_type_data['WeeklyRate'];
        $units = ceil($nights_to_use / 7);
        break;
    case 'monthly':
        $rate_per_unit = $room_type_data['MonthlyRate'];
        $units = ceil($nights_to_use / 30);
        break;
    default:
        $rate_per_unit = $room_type_data['DailyRate'];
        $units = $nights_to_use;
}

$calculated_total = $rate_per_unit * $units * ($booking_type === 'agent' ? $rooms_count : 1);

// Apply discount for travel agents
$discounted_rate_per_room = null;
if ($booking_type === 'agent' && $discount_rate > 0) {
    $discount_amount = $calculated_total * ($discount_rate / 100);
    $discounted_total = $calculated_total - $discount_amount;
    $discounted_rate_per_room = $discounted_total / $rooms_count; // Rate per room after discount
    $calculated_total = $discounted_total;
} else {
    // For non-discounted bookings, calculate rate per room
    $discounted_rate_per_room = $calculated_total / ($booking_type === 'agent' ? $rooms_count : 1);
}

// Verify the total amount matches our calculation (with small tolerance for rounding)
if (abs($calculated_total - $total_amount) > 0.01) {
    echo json_encode(['error' => 'Total amount mismatch']);
    exit();
}

try {
    $conn->begin_transaction();

    if ($booking_type === 'agent') {
        // Travel agent block booking - create multiple reservations
        $reservation_ids = [];
        for ($i = 0; $i < $rooms_count; $i++) {
            $insert_query = "
                INSERT INTO Reservations (
                    CustomerID, HotelID, RoomTypeID, StartDate, EndDate,
                    CompanyID, IsBlockBooking, DiscountedRate, RateType, Status,
                    CreatedAt, UpdatedAt
                ) VALUES (NULL, ?, ?, ?, ?, ?, 1, ?, ?, 'Confirmed', NOW(), NOW())
            ";

            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("iissids", $hotel_id, $room_type_id, $check_in, $check_out, $user_id, $discounted_rate_per_room, $rate_type);

            if (!$stmt->execute()) {
                throw new Exception('Failed to create reservation ' . ($i + 1) . ': ' . $stmt->error);
            }

            $reservation_ids[] = $conn->insert_id;
        }

        $reservation_id = $reservation_ids[0]; // Use first reservation ID for billing

    } else {
        // Customer booking
        $credit_card_to_store = ($payment_method === 'with-card') ? $credit_card_details : null;

        $insert_query = "
            INSERT INTO Reservations (
                CustomerID, HotelID, RoomTypeID, StartDate, EndDate,
                CreditCardDetails, RateType, Status, CreatedAt, UpdatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'Confirmed', NOW(), NOW())
        ";

        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("iisssss", $user_id, $hotel_id, $room_type_id, $check_in, $check_out, $credit_card_to_store, $rate_type);

        if (!$stmt->execute()) {
            throw new Exception('Failed to create reservation: ' . $stmt->error);
        }

        $reservation_id = $conn->insert_id;
    }

    // Create billing record
    $billing_query = "
        INSERT INTO Billing (
            ReservationID, TotalAmount, PaymentMethod, PaymentStatus,
            BillingDateTime, CreatedAt, UpdatedAt
        ) VALUES (?, ?, ?, 'Pending', NOW(), NOW(), NOW())
    ";

    // Determine payment method
    if ($booking_type === 'agent') {
        $billing_payment_method = 'Company Billing';
    } else {
        $billing_payment_method = ($payment_method === 'with-card') ? 'Credit Card' : 'Cash';
    }

    $stmt = $conn->prepare($billing_query);
    $stmt->bind_param("ids", $reservation_id, $total_amount, $billing_payment_method);

    if (!$stmt->execute()) {
        throw new Exception('Failed to create billing record');
    }

    $billing_id = $conn->insert_id;

    $conn->commit();

    // Prepare success response
    $response = [
        'success' => true,
        'reservation_id' => $reservation_id,
        'billing_id' => $billing_id,
        'total_amount' => $total_amount,
        'booking_type' => $booking_type,
        'message' => 'Reservation confirmed successfully!'
    ];

    if ($booking_type === 'agent') {
        $response['rooms_count'] = $rooms_count;
        $response['message'] = "Block booking confirmed successfully! {$rooms_count} rooms reserved. Bills will be charged directly to your company.";
    } elseif ($payment_method === 'without-card') {
        $response['message'] = 'Reservation confirmed! Please note: This reservation will be automatically canceled at 7 PM daily if payment is not received.';
    } else {
        $response['message'] = 'Reservation confirmed successfully! Your credit card details have been securely stored.';
    }

    // Redirect to success page instead of JSON response
    $_SESSION['reservation_success'] = $response;
    header('Location: ../reservation_success.php');
    exit();

} catch (Exception $e) {
    $conn->rollback();

    // Enhanced error reporting for debugging
    $error_details = [
        'error' => 'Failed to create reservation: ' . $e->getMessage(),
        'debug_info' => [
            'booking_type' => $booking_type,
            'user_id' => $user_id,
            'user_type' => $user_type,
            'hotel_id' => $hotel_id,
            'room_type_id' => $room_type_id,
            'check_in' => $check_in,
            'check_out' => $check_out,
            'rate_type' => $rate_type,
            'rooms_count' => $rooms_count,
            'discounted_rate_per_room' => $discounted_rate_per_room,
            'calculated_total' => $calculated_total,
            'total_amount' => $total_amount
        ]
    ];

    echo json_encode($error_details);
}
?>
