<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $employee_id = intval($_POST['employee_id']);
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $role = trim($_POST['role']);
    $hotel_id = !empty($_POST['hotel_id']) ? intval($_POST['hotel_id']) : null;
    
    // Validate input
    if (empty($employee_id) || empty($first_name) || empty($last_name) || empty($email) || empty($role)) {
        echo json_encode(['success' => false, 'message' => 'All fields except hotel are required']);
        exit();
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit();
    }
    
    // Validate role
    $valid_roles = ['Admin', 'Manager', 'Clerk'];
    if (!in_array($role, $valid_roles)) {
        echo json_encode(['success' => false, 'message' => 'Invalid role']);
        exit();
    }
    
    // Validate hotel assignment
    if ($role !== 'Admin' && empty($hotel_id)) {
        echo json_encode(['success' => false, 'message' => 'Non-admin employees must be assigned to a hotel']);
        exit();
    }
    
    if ($role === 'Admin' && !empty($hotel_id)) {
        $hotel_id = null; // Admins should not be assigned to specific hotels
    }
    
    try {
        // Check if email already exists for another employee
        $check_query = "SELECT EmployeeID FROM Employees WHERE Email = ? AND EmployeeID != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $employee_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Email already exists for another employee']);
            exit();
        }
        
        // Update employee
        $update_query = "UPDATE Employees SET FirstName = ?, LastName = ?, Email = ?, Role = ?, HotelID = ?, UpdatedAt = NOW() WHERE EmployeeID = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ssssii", $first_name, $last_name, $email, $role, $hotel_id, $employee_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => 'Employee updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Employee not found or no changes made']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Error updating employee: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
