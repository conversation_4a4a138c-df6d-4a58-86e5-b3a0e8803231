<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    echo "Unauthorized access";
    exit();
}

$bill_id = $_GET['bill_id'] ?? 0;
$clerk_hotel_id = $_SESSION['clerk_hotel_id'];

// Get bill details (same query as in get_bill_details.php)
$bill_query = "
    SELECT b.*,
           r.ReservationID, r.StartDate, r.EndDate, r.NumberOfGuests, r.RateType,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.<PERSON>, 'Guest')) as CustomerName,
           c.Email, c.Phone,
           rm.RoomNumber,
           rt.TypeName as RoomType, rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate,
           ci.CheckInDateTime, ci.ActualCheckOutDateTime,
           h.Name as HotelName, h.Location
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    LEFT JOIN CheckIns ci ON b.CheckInID = ci.CheckInID
    LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    JOIN Hotels h ON r.HotelID = h.HotelID
    WHERE b.BillingID = ? AND r.HotelID = ?
";
$stmt = $conn->prepare($bill_query);
$stmt->bind_param("ii", $bill_id, $clerk_hotel_id);
$stmt->execute();
$bill = $stmt->get_result()->fetch_assoc();

if (!$bill) {
    echo "Bill not found";
    exit();
}

// Get additional charges
$charges_query = "SELECT * FROM AdditionalCharges WHERE BillingID = ? ORDER BY ChargeDate DESC";
$stmt = $conn->prepare($charges_query);
$stmt->bind_param("i", $bill_id);
$stmt->execute();
$charges_result = $stmt->get_result();

// Calculate nights and room charges
$checkin_date = new DateTime($bill['CheckInDateTime'] ?: $bill['StartDate']);
$checkout_date = new DateTime($bill['ActualCheckOutDateTime'] ?: $bill['EndDate']);
$nights = $checkin_date->diff($checkout_date)->days;
if ($nights == 0) $nights = 1;

$room_charges = 0;
if ($bill['RateType'] === 'Weekly') {
    $weeks = ceil($nights / 7);
    $room_charges = $weeks * $bill['WeeklyRate'];
} elseif ($bill['RateType'] === 'Monthly') {
    $months = ceil($nights / 30);
    $room_charges = $months * $bill['MonthlyRate'];
} else {
    $room_charges = $nights * $bill['DailyRate'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill Statement - <?php echo $bill['BillingID']; ?></title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <style>
        @media print {
            .no-print { display: none; }
            body { font-size: 12px; }
        }
        .bill-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .bill-footer {
            border-top: 1px solid #ddd;
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
        }
        .total-row {
            font-size: 1.2em;
            font-weight: bold;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fa fa-print"></i> Print Bill
            </button>
            <button onclick="window.close()" class="btn btn-secondary ml-2">Close</button>
        </div>

        <div class="bill-header">
            <h2><?php echo htmlspecialchars($bill['HotelName']); ?></h2>
            <p><?php echo htmlspecialchars($bill['Location']); ?></p>
            <h4>GUEST BILL STATEMENT</h4>
            <p>Bill No: <?php echo $bill['BillingID']; ?> | Date: <?php echo date('F d, Y', strtotime($bill['BillingDateTime'])); ?></p>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <h6>GUEST INFORMATION</h6>
                <p>
                    <strong>Name:</strong> <?php echo htmlspecialchars($bill['CustomerName']); ?><br>
                    <?php if ($bill['Email']): ?>
                        <strong>Email:</strong> <?php echo htmlspecialchars($bill['Email']); ?><br>
                    <?php endif; ?>
                    <?php if ($bill['Phone']): ?>
                        <strong>Phone:</strong> <?php echo htmlspecialchars($bill['Phone']); ?><br>
                    <?php endif; ?>
                    <strong>Number of Guests:</strong> <?php echo $bill['NumberOfGuests']; ?>
                </p>
            </div>
            <div class="col-md-6">
                <h6>STAY DETAILS</h6>
                <p>
                    <strong>Reservation ID:</strong> <?php echo $bill['ReservationID']; ?><br>
                    <strong>Room:</strong> <?php echo $bill['RoomNumber']; ?> (<?php echo htmlspecialchars($bill['RoomType']); ?>)<br>
                    <strong>Check-in:</strong> <?php echo date('M d, Y H:i', strtotime($bill['CheckInDateTime'] ?: $bill['StartDate'])); ?><br>
                    <strong>Check-out:</strong> <?php echo date('M d, Y H:i', strtotime($bill['ActualCheckOutDateTime'] ?: $bill['EndDate'])); ?><br>
                    <strong>Total Nights:</strong> <?php echo $nights; ?><br>
                    <strong>Rate Type:</strong> <?php echo $bill['RateType']; ?>
                </p>
            </div>
        </div>

        <h6>CHARGES BREAKDOWN</h6>
        <table class="table table-bordered">
            <thead class="thead-light">
                <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th class="text-right">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Room Charges - <?php echo htmlspecialchars($bill['RoomType']); ?></td>
                    <td><?php echo $nights; ?> nights</td>
                    <td>
                        <?php
                        if ($bill['RateType'] === 'Weekly') {
                            echo format_currency($bill['WeeklyRate']) . '/week';
                        } elseif ($bill['RateType'] === 'Monthly') {
                            echo format_currency($bill['MonthlyRate']) . '/month';
                        } else {
                            echo format_currency($bill['DailyRate']) . '/night';
                        }
                        ?>
                    </td>
                    <td class="text-right"><?php echo format_currency($room_charges); ?></td>
                </tr>

                <?php
                $additional_charges_total = 0;
                if ($charges_result->num_rows > 0):
                ?>
                    <?php while ($charge = $charges_result->fetch_assoc()): ?>
                        <?php $additional_charges_total += $charge['Amount']; ?>
                        <tr>
                            <td><?php echo htmlspecialchars($charge['ChargeType']); ?></td>
                            <td>1</td>
                            <td><?php echo htmlspecialchars($charge['Description']); ?></td>
                            <td class="text-right"><?php echo format_currency($charge['Amount']); ?></td>
                        </tr>
                    <?php endwhile; ?>
                <?php endif; ?>

                <?php if ($additional_charges_total > 0): ?>
                <tr>
                    <td colspan="3"><strong>Subtotal (Room)</strong></td>
                    <td class="text-right"><strong><?php echo format_currency($room_charges); ?></strong></td>
                </tr>
                <tr>
                    <td colspan="3"><strong>Subtotal (Additional Charges)</strong></td>
                    <td class="text-right"><strong><?php echo format_currency($additional_charges_total); ?></strong></td>
                </tr>
                <?php endif; ?>

                <tr class="total-row">
                    <td colspan="3"><strong>TOTAL AMOUNT</strong></td>
                    <td class="text-right"><strong><?php echo format_currency($bill['TotalAmount']); ?></strong></td>
                </tr>
            </tbody>
        </table>

        <div class="row mt-4">
            <div class="col-md-6">
                <h6>PAYMENT INFORMATION</h6>
                <p>
                    <strong>Payment Method:</strong> <?php echo htmlspecialchars($bill['PaymentMethod']); ?><br>
                    <strong>Payment Status:</strong>
                    <span class="badge badge-<?php echo $bill['PaymentStatus'] === 'Paid' ? 'success' : 'warning'; ?>">
                        <?php echo htmlspecialchars($bill['PaymentStatus']); ?>
                    </span><br>
                    <strong>Payment Date:</strong> <?php echo date('M d, Y H:i', strtotime($bill['BillingDateTime'])); ?>
                </p>
            </div>
            <div class="col-md-6 text-right">
                <p>
                    <strong>Processed by:</strong> <?php echo htmlspecialchars($_SESSION['clerk_name']); ?><br>
                    <strong>Print Date:</strong> <?php echo date('M d, Y H:i'); ?>
                </p>
            </div>
        </div>

        <div class="bill-footer">
            <p>
                <strong>Thank you for staying with <?php echo htmlspecialchars($bill['HotelName']); ?>!</strong><br>
                We hope you enjoyed your stay and look forward to welcoming you again.
            </p>
            <p class="text-muted">
                <small>
                    This is a computer-generated bill. For any queries, please contact the front desk.<br>
                    Echo Hotels - Your Comfort, Our Priority
                </small>
            </p>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
