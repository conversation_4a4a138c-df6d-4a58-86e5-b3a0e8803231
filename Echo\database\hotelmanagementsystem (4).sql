-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 27, 2025 at 05:34 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `hotelmanagementsystem`
--

-- --------------------------------------------------------

--
-- Table structure for table `additionalcharges`
--

CREATE TABLE `additionalcharges` (
  `ChargeID` int(11) NOT NULL,
  `BillingID` int(11) NOT NULL,
  `ChargeType` enum('Restaurant','RoomService','Laundry','Telephone','ClubFacility') NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `ChargeDate` date NOT NULL,
  `Description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `billing`
--

CREATE TABLE `billing` (
  `BillingID` int(11) NOT NULL,
  `ReservationID` int(11) DEFAULT NULL,
  `CheckInID` int(11) DEFAULT NULL,
  `TotalAmount` decimal(10,2) NOT NULL,
  `PaymentMethod` enum('Cash','CreditCard') NOT NULL,
  `PaymentStatus` enum('Pending','Paid') DEFAULT 'Pending',
  `BillingDateTime` datetime NOT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UpdatedAt` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `checkins`
--

CREATE TABLE `checkins` (
  `CheckInID` int(11) NOT NULL,
  `ReservationID` int(11) DEFAULT NULL,
  `CustomerID` int(11) NOT NULL,
  `HotelID` int(11) NOT NULL,
  `RoomID` int(11) NOT NULL,
  `CheckInDateTime` datetime NOT NULL,
  `ExpectedCheckOutDate` date NOT NULL,
  `ActualCheckOutDateTime` datetime DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UpdatedAt` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `CustomerID` int(11) NOT NULL,
  `FirstName` varchar(50) NOT NULL,
  `LastName` varchar(50) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `Phone` varchar(20) DEFAULT NULL,
  `PasswordHash` varchar(255) NOT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UpdatedAt` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`CustomerID`, `FirstName`, `LastName`, `Email`, `Phone`, `PasswordHash`, `CreatedAt`, `UpdatedAt`) VALUES
(1, 'shan', 'liyanage', 'sahn@123', '0773212132', '$2y$10$hA5w2uE0ngmVuo2cE0gOAe1CFXK6tMskWxqpEF44SuXxnGMUjLdcC', '2025-05-27 13:37:41', '2025-05-27 13:37:41');

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `EmployeeID` int(11) NOT NULL,
  `FirstName` varchar(50) NOT NULL,
  `LastName` varchar(50) NOT NULL,
  `Email` varchar(100) NOT NULL,
  `Role` enum('Admin','ReservationClerk') NOT NULL,
  `HotelID` int(11) DEFAULT NULL,
  `PasswordHash` varchar(255) NOT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UpdatedAt` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employees`
--

INSERT INTO `employees` (`EmployeeID`, `FirstName`, `LastName`, `Email`, `Role`, `HotelID`, `PasswordHash`, `CreatedAt`, `UpdatedAt`) VALUES
(1, 'Admin', 'User', '<EMAIL>', 'Admin', 1, '$2y$10$KDeDejrry5Kl9vdjypF0DekyotZnioME67DDiVyMLsplhvdTxNf2a', '2025-05-27 00:08:08', '2025-05-27 00:08:08');

-- --------------------------------------------------------

--
-- Table structure for table `hotels`
--

CREATE TABLE `hotels` (
  `HotelID` int(11) NOT NULL,
  `Name` varchar(50) NOT NULL,
  `Location` enum('Kandy','Jaffna','Galle') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `hotels`
--

INSERT INTO `hotels` (`HotelID`, `Name`, `Location`) VALUES
(1, 'Kandy Hotel', 'Kandy'),
(2, 'Jaffna Hotel', 'Jaffna'),
(3, 'Galle Hotel', 'Galle');

-- --------------------------------------------------------

--
-- Table structure for table `reservations`
--

CREATE TABLE `reservations` (
  `ReservationID` int(11) NOT NULL,
  `CustomerID` int(11) DEFAULT NULL,
  `HotelID` int(11) NOT NULL,
  `RoomTypeID` int(11) NOT NULL,
  `StartDate` date NOT NULL,
  `EndDate` date NOT NULL,
  `NumberOfGuests` int(11) DEFAULT 1,
  `CreditCardDetails` varchar(255) DEFAULT NULL,
  `Status` enum('Confirmed','Canceled','NoShow','CheckedIn','CheckedOut') DEFAULT 'Confirmed',
  `CompanyID` int(11) DEFAULT NULL,
  `IsBlockBooking` tinyint(1) DEFAULT 0,
  `DiscountedRate` decimal(10,2) DEFAULT NULL,
  `RateType` enum('Daily','Weekly','Monthly') DEFAULT 'Daily',
  `CancellationReason` text DEFAULT NULL,
  `SpecialRequests` text DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UpdatedAt` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rooms`
--

CREATE TABLE `rooms` (
  `RoomID` int(11) NOT NULL,
  `HotelID` int(11) NOT NULL,
  `RoomTypeID` int(11) NOT NULL,
  `RoomNumber` varchar(10) NOT NULL,
  `Status` enum('Available','Occupied','Maintenance') DEFAULT 'Available'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `rooms`
--

INSERT INTO `rooms` (`RoomID`, `HotelID`, `RoomTypeID`, `RoomNumber`, `Status`) VALUES
(1, 1, 1, 'K1-1', 'Available'),
(2, 1, 1, 'K1-2', 'Available'),
(3, 1, 1, 'K1-3', 'Available'),
(4, 1, 1, 'K1-4', 'Available'),
(5, 1, 1, 'K1-5', 'Available'),
(6, 1, 1, 'K1-6', 'Available'),
(7, 1, 1, 'K1-7', 'Available'),
(8, 1, 1, 'K1-8', 'Available'),
(9, 1, 1, 'K1-9', 'Available'),
(10, 1, 1, 'K1-10', 'Available'),
(11, 1, 1, 'K1-11', 'Available'),
(12, 1, 1, 'K1-12', 'Available'),
(13, 1, 1, 'K1-13', 'Available'),
(14, 1, 1, 'K1-14', 'Available'),
(15, 1, 1, 'K1-15', 'Available'),
(16, 1, 1, 'K1-16', 'Available'),
(17, 1, 1, 'K1-17', 'Available'),
(18, 1, 1, 'K1-18', 'Available'),
(19, 1, 1, 'K1-19', 'Available'),
(20, 1, 1, 'K1-20', 'Available'),
(32, 1, 2, 'K2-1', 'Available'),
(33, 1, 2, 'K2-2', 'Available'),
(34, 1, 2, 'K2-3', 'Available'),
(35, 1, 2, 'K2-4', 'Available'),
(36, 1, 2, 'K2-5', 'Available'),
(37, 1, 2, 'K2-6', 'Available'),
(38, 1, 2, 'K2-7', 'Available'),
(39, 1, 2, 'K2-8', 'Available'),
(40, 1, 2, 'K2-9', 'Available'),
(41, 1, 2, 'K2-10', 'Available'),
(42, 1, 2, 'K2-11', 'Available'),
(43, 1, 2, 'K2-12', 'Available'),
(44, 1, 2, 'K2-13', 'Available'),
(45, 1, 2, 'K2-14', 'Available'),
(46, 1, 2, 'K2-15', 'Available'),
(47, 1, 2, 'K2-16', 'Available'),
(48, 1, 2, 'K2-17', 'Available'),
(49, 1, 2, 'K2-18', 'Available'),
(50, 1, 2, 'K2-19', 'Available'),
(51, 1, 2, 'K2-20', 'Available'),
(52, 1, 2, 'K2-21', 'Available'),
(53, 1, 2, 'K2-22', 'Available'),
(54, 1, 2, 'K2-23', 'Available'),
(55, 1, 2, 'K2-24', 'Available'),
(56, 1, 2, 'K2-25', 'Available'),
(57, 1, 2, 'K2-26', 'Available'),
(58, 1, 2, 'K2-27', 'Available'),
(59, 1, 2, 'K2-28', 'Available'),
(60, 1, 2, 'K2-29', 'Available'),
(61, 1, 2, 'K2-30', 'Available'),
(63, 1, 3, 'K3-1', 'Available'),
(64, 1, 3, 'K3-2', 'Available'),
(65, 1, 3, 'K3-3', 'Available'),
(66, 1, 3, 'K3-4', 'Available'),
(67, 1, 3, 'K3-5', 'Available'),
(68, 1, 3, 'K3-6', 'Available'),
(69, 1, 3, 'K3-7', 'Available'),
(70, 1, 3, 'K3-8', 'Available'),
(71, 1, 3, 'K3-9', 'Available'),
(72, 1, 3, 'K3-10', 'Available'),
(78, 1, 4, 'K4-1', 'Available'),
(79, 1, 4, 'K4-2', 'Available'),
(80, 1, 4, 'K4-3', 'Available'),
(81, 1, 4, 'K4-4', 'Available'),
(82, 1, 4, 'K4-5', 'Available'),
(83, 1, 4, 'K4-6', 'Available'),
(84, 1, 4, 'K4-7', 'Available'),
(85, 1, 4, 'K4-8', 'Available'),
(86, 1, 4, 'K4-9', 'Available'),
(87, 1, 4, 'K4-10', 'Available'),
(93, 2, 1, 'J1-1', 'Available'),
(94, 2, 1, 'J1-2', 'Available'),
(95, 2, 1, 'J1-3', 'Available'),
(96, 2, 1, 'J1-4', 'Available'),
(97, 2, 1, 'J1-5', 'Available'),
(98, 2, 1, 'J1-6', 'Available'),
(99, 2, 1, 'J1-7', 'Available'),
(100, 2, 1, 'J1-8', 'Available'),
(101, 2, 1, 'J1-9', 'Available'),
(102, 2, 1, 'J1-10', 'Available'),
(103, 2, 1, 'J1-11', 'Available'),
(104, 2, 1, 'J1-12', 'Available'),
(105, 2, 1, 'J1-13', 'Available'),
(106, 2, 1, 'J1-14', 'Available'),
(107, 2, 1, 'J1-15', 'Available'),
(108, 2, 1, 'J1-16', 'Available'),
(109, 2, 1, 'J1-17', 'Available'),
(110, 2, 1, 'J1-18', 'Available'),
(111, 2, 1, 'J1-19', 'Available'),
(112, 2, 1, 'J1-20', 'Available'),
(124, 2, 2, 'J2-1', 'Available'),
(125, 2, 2, 'J2-2', 'Available'),
(126, 2, 2, 'J2-3', 'Available'),
(127, 2, 2, 'J2-4', 'Available'),
(128, 2, 2, 'J2-5', 'Available'),
(129, 2, 2, 'J2-6', 'Available'),
(130, 2, 2, 'J2-7', 'Available'),
(131, 2, 2, 'J2-8', 'Available'),
(132, 2, 2, 'J2-9', 'Available'),
(133, 2, 2, 'J2-10', 'Available'),
(134, 2, 2, 'J2-11', 'Available'),
(135, 2, 2, 'J2-12', 'Available'),
(136, 2, 2, 'J2-13', 'Available'),
(137, 2, 2, 'J2-14', 'Available'),
(138, 2, 2, 'J2-15', 'Available'),
(139, 2, 2, 'J2-16', 'Available'),
(140, 2, 2, 'J2-17', 'Available'),
(141, 2, 2, 'J2-18', 'Available'),
(142, 2, 2, 'J2-19', 'Available'),
(143, 2, 2, 'J2-20', 'Available'),
(144, 2, 2, 'J2-21', 'Available'),
(145, 2, 2, 'J2-22', 'Available'),
(146, 2, 2, 'J2-23', 'Available'),
(147, 2, 2, 'J2-24', 'Available'),
(148, 2, 2, 'J2-25', 'Available'),
(149, 2, 2, 'J2-26', 'Available'),
(150, 2, 2, 'J2-27', 'Available'),
(151, 2, 2, 'J2-28', 'Available'),
(152, 2, 2, 'J2-29', 'Available'),
(153, 2, 2, 'J2-30', 'Available'),
(155, 2, 3, 'J3-1', 'Available'),
(156, 2, 3, 'J3-2', 'Available'),
(157, 2, 3, 'J3-3', 'Available'),
(158, 2, 3, 'J3-4', 'Available'),
(159, 2, 3, 'J3-5', 'Available'),
(160, 2, 3, 'J3-6', 'Available'),
(161, 2, 3, 'J3-7', 'Available'),
(162, 2, 3, 'J3-8', 'Available'),
(163, 2, 3, 'J3-9', 'Available'),
(164, 2, 3, 'J3-10', 'Available'),
(170, 2, 4, 'J4-1', 'Available'),
(171, 2, 4, 'J4-2', 'Available'),
(172, 2, 4, 'J4-3', 'Available'),
(173, 2, 4, 'J4-4', 'Available'),
(174, 2, 4, 'J4-5', 'Available'),
(175, 2, 4, 'J4-6', 'Available'),
(176, 2, 4, 'J4-7', 'Available'),
(177, 2, 4, 'J4-8', 'Available'),
(178, 2, 4, 'J4-9', 'Available'),
(179, 2, 4, 'J4-10', 'Available'),
(185, 3, 1, 'G1-1', 'Available'),
(186, 3, 1, 'G1-2', 'Available'),
(187, 3, 1, 'G1-3', 'Available'),
(188, 3, 1, 'G1-4', 'Available'),
(189, 3, 1, 'G1-5', 'Available'),
(190, 3, 1, 'G1-6', 'Available'),
(191, 3, 1, 'G1-7', 'Available'),
(192, 3, 1, 'G1-8', 'Available'),
(193, 3, 1, 'G1-9', 'Available'),
(194, 3, 1, 'G1-10', 'Available'),
(195, 3, 1, 'G1-11', 'Available'),
(196, 3, 1, 'G1-12', 'Available'),
(197, 3, 1, 'G1-13', 'Available'),
(198, 3, 1, 'G1-14', 'Available'),
(199, 3, 1, 'G1-15', 'Available'),
(200, 3, 1, 'G1-16', 'Available'),
(201, 3, 1, 'G1-17', 'Available'),
(202, 3, 1, 'G1-18', 'Available'),
(203, 3, 1, 'G1-19', 'Available'),
(204, 3, 1, 'G1-20', 'Available'),
(216, 3, 2, 'G2-1', 'Available'),
(217, 3, 2, 'G2-2', 'Available'),
(218, 3, 2, 'G2-3', 'Available'),
(219, 3, 2, 'G2-4', 'Available'),
(220, 3, 2, 'G2-5', 'Available'),
(221, 3, 2, 'G2-6', 'Available'),
(222, 3, 2, 'G2-7', 'Available'),
(223, 3, 2, 'G2-8', 'Available'),
(224, 3, 2, 'G2-9', 'Available'),
(225, 3, 2, 'G2-10', 'Available'),
(226, 3, 2, 'G2-11', 'Available'),
(227, 3, 2, 'G2-12', 'Available'),
(228, 3, 2, 'G2-13', 'Available'),
(229, 3, 2, 'G2-14', 'Available'),
(230, 3, 2, 'G2-15', 'Available'),
(231, 3, 2, 'G2-16', 'Available'),
(232, 3, 2, 'G2-17', 'Available'),
(233, 3, 2, 'G2-18', 'Available'),
(234, 3, 2, 'G2-19', 'Available'),
(235, 3, 2, 'G2-20', 'Available'),
(236, 3, 2, 'G2-21', 'Available'),
(237, 3, 2, 'G2-22', 'Available'),
(238, 3, 2, 'G2-23', 'Available'),
(239, 3, 2, 'G2-24', 'Available'),
(240, 3, 2, 'G2-25', 'Available'),
(241, 3, 2, 'G2-26', 'Available'),
(242, 3, 2, 'G2-27', 'Available'),
(243, 3, 2, 'G2-28', 'Available'),
(244, 3, 2, 'G2-29', 'Available'),
(245, 3, 2, 'G2-30', 'Available'),
(247, 3, 3, 'G3-1', 'Available'),
(248, 3, 3, 'G3-2', 'Available'),
(249, 3, 3, 'G3-3', 'Available'),
(250, 3, 3, 'G3-4', 'Available'),
(251, 3, 3, 'G3-5', 'Available'),
(252, 3, 3, 'G3-6', 'Available'),
(253, 3, 3, 'G3-7', 'Available'),
(254, 3, 3, 'G3-8', 'Available'),
(255, 3, 3, 'G3-9', 'Available'),
(256, 3, 3, 'G3-10', 'Available'),
(262, 3, 4, 'G4-1', 'Available'),
(263, 3, 4, 'G4-2', 'Available'),
(264, 3, 4, 'G4-3', 'Available'),
(265, 3, 4, 'G4-4', 'Available'),
(266, 3, 4, 'G4-5', 'Available'),
(267, 3, 4, 'G4-6', 'Available'),
(268, 3, 4, 'G4-7', 'Available'),
(269, 3, 4, 'G4-8', 'Available'),
(270, 3, 4, 'G4-9', 'Available'),
(271, 3, 4, 'G4-10', 'Available');

-- --------------------------------------------------------

--
-- Table structure for table `roomtypes`
--

CREATE TABLE `roomtypes` (
  `RoomTypeID` int(11) NOT NULL,
  `TypeName` varchar(50) NOT NULL,
  `DailyRate` decimal(10,2) NOT NULL,
  `WeeklyRate` decimal(10,2) DEFAULT NULL,
  `MonthlyRate` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `roomtypes`
--

INSERT INTO `roomtypes` (`RoomTypeID`, `TypeName`, `DailyRate`, `WeeklyRate`, `MonthlyRate`) VALUES
(1, 'Deluxe', 15000.00, 90000.00, 350000.00),
(2, 'Double Room', 10000.00, 60000.00, 200000.00),
(3, 'Premium Luxury Rooms', 25000.00, 150000.00, 500000.00),
(4, 'Residential Suite', 20000.00, 100000.00, 350000.00);

-- --------------------------------------------------------

--
-- Table structure for table `travelcompanies`
--

CREATE TABLE `travelcompanies` (
  `CompanyID` int(11) NOT NULL,
  `CompanyName` varchar(100) NOT NULL,
  `ContactEmail` varchar(100) NOT NULL,
  `ContactPhone` varchar(20) NOT NULL,
  `Address` text NOT NULL,
  `ContractDetails` text DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UpdatedAt` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `additionalcharges`
--
ALTER TABLE `additionalcharges`
  ADD PRIMARY KEY (`ChargeID`),
  ADD KEY `BillingID` (`BillingID`);

--
-- Indexes for table `billing`
--
ALTER TABLE `billing`
  ADD PRIMARY KEY (`BillingID`),
  ADD KEY `ReservationID` (`ReservationID`),
  ADD KEY `CheckInID` (`CheckInID`);

--
-- Indexes for table `checkins`
--
ALTER TABLE `checkins`
  ADD PRIMARY KEY (`CheckInID`),
  ADD KEY `ReservationID` (`ReservationID`),
  ADD KEY `CustomerID` (`CustomerID`),
  ADD KEY `HotelID` (`HotelID`),
  ADD KEY `RoomID` (`RoomID`),
  ADD KEY `idx_checkins_dates` (`CheckInDateTime`,`ActualCheckOutDateTime`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`CustomerID`),
  ADD UNIQUE KEY `Email` (`Email`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`EmployeeID`),
  ADD UNIQUE KEY `Email` (`Email`),
  ADD KEY `HotelID` (`HotelID`);

--
-- Indexes for table `hotels`
--
ALTER TABLE `hotels`
  ADD PRIMARY KEY (`HotelID`);

--
-- Indexes for table `reservations`
--
ALTER TABLE `reservations`
  ADD PRIMARY KEY (`ReservationID`),
  ADD KEY `CustomerID` (`CustomerID`),
  ADD KEY `HotelID` (`HotelID`),
  ADD KEY `RoomTypeID` (`RoomTypeID`),
  ADD KEY `CompanyID` (`CompanyID`),
  ADD KEY `idx_reservations_dates` (`StartDate`,`EndDate`);

--
-- Indexes for table `rooms`
--
ALTER TABLE `rooms`
  ADD PRIMARY KEY (`RoomID`),
  ADD KEY `RoomTypeID` (`RoomTypeID`),
  ADD KEY `idx_rooms_availability` (`HotelID`,`RoomTypeID`,`Status`);

--
-- Indexes for table `roomtypes`
--
ALTER TABLE `roomtypes`
  ADD PRIMARY KEY (`RoomTypeID`);

--
-- Indexes for table `travelcompanies`
--
ALTER TABLE `travelcompanies`
  ADD PRIMARY KEY (`CompanyID`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `additionalcharges`
--
ALTER TABLE `additionalcharges`
  MODIFY `ChargeID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `billing`
--
ALTER TABLE `billing`
  MODIFY `BillingID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `checkins`
--
ALTER TABLE `checkins`
  MODIFY `CheckInID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `CustomerID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `EmployeeID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `hotels`
--
ALTER TABLE `hotels`
  MODIFY `HotelID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `reservations`
--
ALTER TABLE `reservations`
  MODIFY `ReservationID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rooms`
--
ALTER TABLE `rooms`
  MODIFY `RoomID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=272;

--
-- AUTO_INCREMENT for table `roomtypes`
--
ALTER TABLE `roomtypes`
  MODIFY `RoomTypeID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `travelcompanies`
--
ALTER TABLE `travelcompanies`
  MODIFY `CompanyID` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `additionalcharges`
--
ALTER TABLE `additionalcharges`
  ADD CONSTRAINT `additionalcharges_ibfk_1` FOREIGN KEY (`BillingID`) REFERENCES `billing` (`BillingID`);

--
-- Constraints for table `billing`
--
ALTER TABLE `billing`
  ADD CONSTRAINT `billing_ibfk_1` FOREIGN KEY (`ReservationID`) REFERENCES `reservations` (`ReservationID`),
  ADD CONSTRAINT `billing_ibfk_2` FOREIGN KEY (`CheckInID`) REFERENCES `checkins` (`CheckInID`);

--
-- Constraints for table `checkins`
--
ALTER TABLE `checkins`
  ADD CONSTRAINT `checkins_ibfk_1` FOREIGN KEY (`ReservationID`) REFERENCES `reservations` (`ReservationID`),
  ADD CONSTRAINT `checkins_ibfk_2` FOREIGN KEY (`CustomerID`) REFERENCES `customers` (`CustomerID`),
  ADD CONSTRAINT `checkins_ibfk_3` FOREIGN KEY (`HotelID`) REFERENCES `hotels` (`HotelID`),
  ADD CONSTRAINT `checkins_ibfk_4` FOREIGN KEY (`RoomID`) REFERENCES `rooms` (`RoomID`);

--
-- Constraints for table `employees`
--
ALTER TABLE `employees`
  ADD CONSTRAINT `employees_ibfk_1` FOREIGN KEY (`HotelID`) REFERENCES `hotels` (`HotelID`);

--
-- Constraints for table `reservations`
--
ALTER TABLE `reservations`
  ADD CONSTRAINT `reservations_ibfk_1` FOREIGN KEY (`CustomerID`) REFERENCES `customers` (`CustomerID`),
  ADD CONSTRAINT `reservations_ibfk_2` FOREIGN KEY (`HotelID`) REFERENCES `hotels` (`HotelID`),
  ADD CONSTRAINT `reservations_ibfk_3` FOREIGN KEY (`RoomTypeID`) REFERENCES `roomtypes` (`RoomTypeID`),
  ADD CONSTRAINT `reservations_ibfk_4` FOREIGN KEY (`CompanyID`) REFERENCES `travelcompanies` (`CompanyID`);

--
-- Constraints for table `rooms`
--
ALTER TABLE `rooms`
  ADD CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`HotelID`) REFERENCES `hotels` (`HotelID`),
  ADD CONSTRAINT `rooms_ibfk_2` FOREIGN KEY (`RoomTypeID`) REFERENCES `roomtypes` (`RoomTypeID`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
