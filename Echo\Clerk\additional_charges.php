<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';

// Handle adding additional charges
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_charge'])) {
    $checkin_id = $_POST['checkin_id'];
    $charge_type = $_POST['charge_type'];
    $amount = $_POST['amount'];
    $description = $_POST['description'];

    try {
        // Get billing ID for this check-in
        $billing_query = "
            SELECT b.BillingID
            FROM Billing b
            JOIN CheckIns ci ON b.CheckInID = ci.CheckInID
            JOIN Reservations r ON ci.ReservationID = r.ReservationID
            WHERE ci.CheckInID = ? AND r.HotelID = ?
        ";
        $stmt = $conn->prepare($billing_query);
        $stmt->bind_param("ii", $checkin_id, $clerk_hotel_id);
        $stmt->execute();
        $billing_result = $stmt->get_result()->fetch_assoc();

        if (!$billing_result) {
            throw new Exception('Billing record not found for this guest');
        }

        $billing_id = $billing_result['BillingID'];

        // Add the charge
        $charge_query = "
            INSERT INTO AdditionalCharges (
                BillingID, ChargeType, Amount, ChargeDate, Description
            ) VALUES (?, ?, ?, CURDATE(), ?)
        ";
        $stmt = $conn->prepare($charge_query);
        $stmt->bind_param("isds", $billing_id, $charge_type, $amount, $description);

        if ($stmt->execute()) {
            // Update total amount in billing
            $update_billing_query = "
                UPDATE Billing
                SET TotalAmount = TotalAmount + ?, UpdatedAt = NOW()
                WHERE BillingID = ?
            ";
            $stmt = $conn->prepare($update_billing_query);
            $stmt->bind_param("di", $amount, $billing_id);
            $stmt->execute();

            $message = 'Additional charge added successfully!';
            $message_type = 'success';
        } else {
            throw new Exception('Failed to add charge');
        }

    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current guests with their billing information
$current_guests_query = "
    SELECT ci.CheckInID, ci.CheckInDateTime,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           rm.RoomNumber,
           rt.TypeName as RoomType,
           b.BillingID, b.TotalAmount,
           (SELECT SUM(ac.Amount) FROM AdditionalCharges ac WHERE ac.BillingID = b.BillingID) as AdditionalChargesTotal
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN Rooms rm ON ci.RoomID = rm.RoomID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    LEFT JOIN Billing b ON ci.CheckInID = b.CheckInID
    WHERE r.HotelID = ? AND ci.ActualCheckOutDateTime IS NULL
    ORDER BY rm.RoomNumber
";
$stmt = $conn->prepare($current_guests_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$current_guests = $stmt->get_result();

// Get recent charges
$recent_charges_query = "
    SELECT ac.*,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           rm.RoomNumber
    FROM AdditionalCharges ac
    JOIN Billing b ON ac.BillingID = b.BillingID
    JOIN CheckIns ci ON b.CheckInID = ci.CheckInID
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN Rooms rm ON ci.RoomID = rm.RoomID
    WHERE r.HotelID = ? AND ac.ChargeDate >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    ORDER BY ac.ChargeDate DESC, ac.ChargeID DESC
    LIMIT 20
";
$stmt = $conn->prepare($recent_charges_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$recent_charges = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Additional Charges - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Additional Charges</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <!-- Current Guests -->
                <div class="card">
                    <div class="card-header">
                        <h3>Current Guests - Add Charges</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Room</th>
                                        <th>Guest Name</th>
                                        <th>Room Type</th>
                                        <th>Check-in Date</th>
                                        <th>Current Bill</th>
                                        <th>Additional Charges</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($current_guests->num_rows > 0): ?>
                                        <?php while ($guest = $current_guests->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($guest['RoomNumber']); ?></td>
                                            <td><?php echo htmlspecialchars($guest['CustomerName']); ?></td>
                                            <td><?php echo htmlspecialchars($guest['RoomType']); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($guest['CheckInDateTime'])); ?></td>
                                            <td><?php echo format_currency($guest['TotalAmount'] ?? 0); ?></td>
                                            <td><?php echo format_currency($guest['AdditionalChargesTotal'] ?? 0); ?></td>
                                            <td>
                                                <button class="btn btn-primary btn-sm"
                                                        onclick="addCharge(<?php echo $guest['CheckInID']; ?>,
                                                                          '<?php echo htmlspecialchars($guest['CustomerName']); ?>',
                                                                          '<?php echo $guest['RoomNumber']; ?>')">
                                                    <i class="fa fa-plus"></i> Add Charge
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No guests currently checked in</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Recent Charges -->
                <div class="card">
                    <div class="card-header">
                        <h3>Recent Additional Charges (Last 7 Days)</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Room</th>
                                        <th>Guest</th>
                                        <th>Charge Type</th>
                                        <th>Amount</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($recent_charges->num_rows > 0): ?>
                                        <?php while ($charge = $recent_charges->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $charge['ChargeDate']; ?></td>
                                            <td><?php echo htmlspecialchars($charge['RoomNumber']); ?></td>
                                            <td><?php echo htmlspecialchars($charge['CustomerName']); ?></td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <?php echo htmlspecialchars($charge['ChargeType']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo format_currency($charge['Amount']); ?></td>
                                            <td><?php echo htmlspecialchars($charge['Description']); ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No recent additional charges</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Charge Modal -->
    <div class="modal fade" id="addChargeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Additional Charge</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="add_charge" value="1">
                        <input type="hidden" id="checkin_id" name="checkin_id">

                        <div class="form-group">
                            <label>Guest Information</label>
                            <input type="text" id="guest_info" class="form-control" readonly>
                        </div>

                        <div class="form-group">
                            <label>Charge Type *</label>
                            <select name="charge_type" class="form-control" required>
                                <option value="">Select charge type</option>
                                <option value="Restaurant">Restaurant</option>
                                <option value="RoomService">Room Service</option>
                                <option value="Laundry">Laundry</option>
                                <option value="Telephone">Telephone</option>
                                <option value="ClubFacility">Club Facility</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Amount *</label>
                            <input type="number" name="amount" class="form-control"
                                   step="0.01" min="0.01" required placeholder="0.00">
                        </div>

                        <div class="form-group">
                            <label>Description</label>
                            <textarea name="description" class="form-control" rows="3"
                                      placeholder="Description of the charge..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Add Charge
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        function addCharge(checkinId, guestName, roomNumber) {
            document.getElementById('checkin_id').value = checkinId;
            document.getElementById('guest_info').value = guestName + ' - Room ' + roomNumber;

            $('#addChargeModal').modal('show');
        }
    </script>
</body>
</html>
