<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];

// Get today's statistics
$today = date('Y-m-d');

// Today's check-ins
$checkins_query = "
    SELECT COUNT(*) as count 
    FROM CheckIns ci 
    JOIN Reservations r ON ci.ReservationID = r.ReservationID 
    WHERE DATE(ci.CheckInDateTime) = ? AND r.HotelID = ?
";
$stmt = $conn->prepare($checkins_query);
$stmt->bind_param("si", $today, $clerk_hotel_id);
$stmt->execute();
$todays_checkins = $stmt->get_result()->fetch_assoc()['count'];

// Today's checkouts
$checkouts_query = "
    SELECT COUNT(*) as count 
    FROM CheckIns ci 
    JOIN Reservations r ON ci.ReservationID = r.ReservationID 
    WHERE DATE(ci.ActualCheckOutDateTime) = ? AND r.HotelID = ?
";
$stmt = $conn->prepare($checkouts_query);
$stmt->bind_param("si", $today, $clerk_hotel_id);
$stmt->execute();
$todays_checkouts = $stmt->get_result()->fetch_assoc()['count'];

// Pending check-ins (reservations for today not yet checked in)
$pending_checkins_query = "
    SELECT COUNT(*) as count 
    FROM Reservations r 
    WHERE r.StartDate = ? AND r.HotelID = ? AND r.Status = 'Confirmed'
    AND NOT EXISTS (SELECT 1 FROM CheckIns ci WHERE ci.ReservationID = r.ReservationID)
";
$stmt = $conn->prepare($pending_checkins_query);
$stmt->bind_param("si", $today, $clerk_hotel_id);
$stmt->execute();
$pending_checkins = $stmt->get_result()->fetch_assoc()['count'];

// Current occupancy
$occupancy_query = "
    SELECT COUNT(*) as occupied_rooms
    FROM CheckIns ci 
    JOIN Reservations r ON ci.ReservationID = r.ReservationID 
    WHERE r.HotelID = ? AND ci.ActualCheckOutDateTime IS NULL
";
$stmt = $conn->prepare($occupancy_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$occupied_rooms = $stmt->get_result()->fetch_assoc()['occupied_rooms'];

// Total rooms in hotel
$total_rooms_query = "SELECT COUNT(*) as total FROM Rooms WHERE HotelID = ? AND Status = 'Available'";
$stmt = $conn->prepare($total_rooms_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$total_rooms = $stmt->get_result()->fetch_assoc()['total'];

// Get hotel name
$hotel_query = "SELECT Name FROM Hotels WHERE HotelID = ?";
$stmt = $conn->prepare($hotel_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$hotel_name = $stmt->get_result()->fetch_assoc()['Name'];

// Recent reservations
$recent_reservations_query = "
    SELECT r.*, 
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           rt.TypeName as RoomType,
           ci.CheckInDateTime,
           ci.ActualCheckOutDateTime
    FROM Reservations r
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    LEFT JOIN CheckIns ci ON r.ReservationID = ci.ReservationID
    WHERE r.HotelID = ?
    ORDER BY r.CreatedAt DESC
    LIMIT 10
";
$stmt = $conn->prepare($recent_reservations_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$recent_reservations = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clerk Dashboard - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="header">
                <h1>Dashboard - <?php echo htmlspecialchars($hotel_name); ?></h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>
            
            <div class="content">
                <!-- Statistics Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card primary">
                            <h3><?php echo $todays_checkins; ?></h3>
                            <p>Today's Check-ins</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card success">
                            <h3><?php echo $todays_checkouts; ?></h3>
                            <p>Today's Check-outs</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card warning">
                            <h3><?php echo $pending_checkins; ?></h3>
                            <p>Pending Check-ins</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card info">
                            <h3><?php echo $occupied_rooms; ?>/<?php echo $total_rooms; ?></h3>
                            <p>Current Occupancy</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="checkin.php" class="btn btn-primary btn-block">
                                            <i class="fa fa-sign-in"></i> Check-In Guest
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="checkout.php" class="btn btn-success btn-block">
                                            <i class="fa fa-sign-out"></i> Check-Out Guest
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="walk_in.php" class="btn btn-info btn-block">
                                            <i class="fa fa-user-plus"></i> Walk-In Guest
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="room_status.php" class="btn btn-warning btn-block">
                                            <i class="fa fa-bed"></i> Room Status
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Reservations -->
                <div class="card">
                    <div class="card-header">
                        <h3>Recent Reservations</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Guest Name</th>
                                        <th>Room Type</th>
                                        <th>Check-in Date</th>
                                        <th>Check-out Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($reservation = $recent_reservations->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $reservation['ReservationID']; ?></td>
                                        <td><?php echo htmlspecialchars($reservation['CustomerName']); ?></td>
                                        <td><?php echo htmlspecialchars($reservation['RoomType']); ?></td>
                                        <td><?php echo $reservation['StartDate']; ?></td>
                                        <td><?php echo $reservation['EndDate']; ?></td>
                                        <td>
                                            <?php
                                            $status_class = 'secondary';
                                            if ($reservation['Status'] === 'Confirmed') $status_class = 'primary';
                                            elseif ($reservation['Status'] === 'CheckedIn') $status_class = 'success';
                                            elseif ($reservation['Status'] === 'CheckedOut') $status_class = 'info';
                                            elseif ($reservation['Status'] === 'Canceled') $status_class = 'danger';
                                            ?>
                                            <span class="badge badge-<?php echo $status_class; ?>">
                                                <?php echo $reservation['Status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($reservation['Status'] === 'Confirmed'): ?>
                                                <a href="checkin.php?reservation_id=<?php echo $reservation['ReservationID']; ?>" 
                                                   class="btn btn-sm btn-primary">Check-In</a>
                                            <?php elseif ($reservation['Status'] === 'CheckedIn'): ?>
                                                <a href="checkout.php?reservation_id=<?php echo $reservation['ReservationID']; ?>" 
                                                   class="btn btn-sm btn-success">Check-Out</a>
                                            <?php endif; ?>
                                            <a href="reservations.php?view=<?php echo $reservation['ReservationID']; ?>" 
                                               class="btn btn-sm btn-info">View</a>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
</body>
</html>
