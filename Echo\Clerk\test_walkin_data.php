<?php
session_start();
require_once '../config/db.php';

// Check if user is logged in as clerk
if (!isset($_SESSION['clerk_id'])) {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];

// Get recent walk-in guests (customers created today)
$recent_customers_query = "
    SELECT
        c.CustomerID,
        c.FirstName,
        c.LastName,
        c.Email,
        c.Phone,
        c.CreatedAt,
        r.ReservationID,
        r.Status as ReservationStatus,
        ci.CheckInID,
        ci.CheckInDateTime,
        ci.RoomID,
        rm.RoomNumber,
        rt.TypeName as RoomType
    FROM Customers c
    LEFT JOIN Reservations r ON c.CustomerID = r.CustomerID
    LEFT JOIN CheckIns ci ON r.ReservationID = ci.ReservationID
    LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
    LEFT JOIN RoomTypes rt ON rm.RoomTypeID = rt.RoomTypeID
    WHERE DATE(c.CreatedAt) = CURDATE()
    AND r.HotelID = ?
    ORDER BY c.CreatedAt DESC
    LIMIT 20
";

$stmt = $conn->prepare($recent_customers_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$recent_guests = $stmt->get_result();

// Get total counts
$customer_count_query = "SELECT COUNT(*) as total FROM Customers WHERE DATE(CreatedAt) = CURDATE()";
$customer_count = $conn->query($customer_count_query)->fetch_assoc()['total'];

$checkin_count_query = "
    SELECT COUNT(*) as total
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    WHERE DATE(ci.CheckInDateTime) = CURDATE() AND r.HotelID = ?
";
$stmt = $conn->prepare($checkin_count_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$checkin_count = $stmt->get_result()->fetch_assoc()['total'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Walk-In Data Verification - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1><i class="fa fa-database"></i> Walk-In Data Verification</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="stats-card success">
                            <h3><?php echo $customer_count; ?></h3>
                            <p><i class="fa fa-users"></i> New Customers Today</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card info">
                            <h3><?php echo $checkin_count; ?></h3>
                            <p><i class="fa fa-sign-in"></i> Check-ins Today</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Walk-in Guests -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fa fa-list"></i> Recent Walk-In Guests (Today)</h3>
                        <p class="mb-0 text-muted">
                            <small>Showing customers created today with their check-in records</small>
                        </p>
                    </div>
                    <div class="card-body">
                        <?php if ($recent_guests->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Customer ID</th>
                                        <th>Guest Name</th>
                                        <th>Contact</th>
                                        <th>Check-in ID</th>
                                        <th>Room</th>
                                        <th>Status</th>
                                        <th>Created At</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($guest = $recent_guests->fetch_assoc()): ?>
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary"><?php echo $guest['CustomerID']; ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($guest['FirstName'] . ' ' . $guest['LastName']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($guest['Email']): ?>
                                                <i class="fa fa-envelope"></i> <?php echo htmlspecialchars($guest['Email']); ?><br>
                                            <?php endif; ?>
                                            <?php if ($guest['Phone']): ?>
                                                <i class="fa fa-phone"></i> <?php echo htmlspecialchars($guest['Phone']); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($guest['CheckInID']): ?>
                                                <span class="badge badge-success"><?php echo $guest['CheckInID']; ?></span>
                                            <?php else: ?>
                                                <span class="badge badge-warning">No Check-in</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($guest['RoomNumber']): ?>
                                                <strong><?php echo $guest['RoomNumber']; ?></strong><br>
                                                <small class="text-muted"><?php echo $guest['RoomType']; ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">No Room</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($guest['ReservationStatus']): ?>
                                                <span class="badge badge-<?php echo $guest['ReservationStatus'] === 'CheckedIn' ? 'success' : 'info'; ?>">
                                                    <?php echo $guest['ReservationStatus']; ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('H:i:s', strtotime($guest['CreatedAt'])); ?></small>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fa fa-info-circle fa-3x text-muted mb-3"></i>
                            <h5>No walk-in guests registered today</h5>
                            <p class="text-muted">Use the <a href="walk_in.php">Walk-In Guest</a> form to register new guests.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mt-4">
                    <a href="walk_in.php" class="btn btn-primary btn-lg">
                        <i class="fa fa-user-plus"></i> Register New Walk-In Guest
                    </a>
                    <a href="dashboard.php" class="btn btn-secondary btn-lg ml-2">
                        <i class="fa fa-dashboard"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
</body>
</html>
