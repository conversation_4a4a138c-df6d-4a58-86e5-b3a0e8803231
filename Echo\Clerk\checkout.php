<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';

// Handle checkout process
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $checkin_id = $_POST['checkin_id'];
    $payment_method = $_POST['payment_method'];
    $additional_charges = $_POST['additional_charges'] ?? 0;
    $late_checkout_charge = $_POST['late_checkout_charge'] ?? 0;
    
    try {
        $conn->begin_transaction();
        
        // Get check-in details
        $checkin_query = "
            SELECT ci.*, r.<PERSON>, r.<PERSON>, r.<PERSON>, r.Start<PERSON>ate, r.<PERSON>, r.RateType,
                   rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate,
                   rm.RoomNumber, rm.RoomID,
                   CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName
            FROM CheckIns ci
            JOIN Reservations r ON ci.ReservationID = r.ReservationID
            JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            JOIN Rooms rm ON ci.RoomID = rm.RoomID
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            WHERE ci.CheckInID = ? AND r.HotelID = ? AND ci.ActualCheckOutDateTime IS NULL
        ";
        $stmt = $conn->prepare($checkin_query);
        $stmt->bind_param("ii", $checkin_id, $clerk_hotel_id);
        $stmt->execute();
        $checkin_data = $stmt->get_result()->fetch_assoc();
        
        if (!$checkin_data) {
            throw new Exception('Check-in record not found or already checked out');
        }
        
        // Calculate total bill
        $checkin_date = new DateTime($checkin_data['CheckInDateTime']);
        $checkout_date = new DateTime();
        $nights = $checkin_date->diff($checkout_date)->days;
        if ($nights == 0) $nights = 1; // Minimum 1 night charge
        
        // Calculate room charges based on rate type
        $room_charges = 0;
        if ($checkin_data['RateType'] === 'Weekly') {
            $weeks = ceil($nights / 7);
            $room_charges = $weeks * $checkin_data['WeeklyRate'];
        } elseif ($checkin_data['RateType'] === 'Monthly') {
            $months = ceil($nights / 30);
            $room_charges = $months * $checkin_data['MonthlyRate'];
        } else {
            $room_charges = $nights * $checkin_data['DailyRate'];
        }
        
        $total_amount = $room_charges + $additional_charges + $late_checkout_charge;
        
        // Update check-in record
        $update_checkin_query = "
            UPDATE CheckIns 
            SET ActualCheckOutDateTime = NOW(), UpdatedAt = NOW() 
            WHERE CheckInID = ?
        ";
        $stmt = $conn->prepare($update_checkin_query);
        $stmt->bind_param("i", $checkin_id);
        $stmt->execute();
        
        // Update reservation status
        $update_reservation_query = "
            UPDATE Reservations 
            SET Status = 'CheckedOut', UpdatedAt = NOW() 
            WHERE ReservationID = ?
        ";
        $stmt = $conn->prepare($update_reservation_query);
        $stmt->bind_param("i", $checkin_data['ReservationID']);
        $stmt->execute();
        
        // Update room status
        $update_room_query = "UPDATE Rooms SET Status = 'Available' WHERE RoomID = ?";
        $stmt = $conn->prepare($update_room_query);
        $stmt->bind_param("i", $checkin_data['RoomID']);
        $stmt->execute();
        
        // Create or update billing record
        $billing_query = "
            INSERT INTO Billing (
                ReservationID, CheckInID, TotalAmount, PaymentMethod, 
                PaymentStatus, BillingDateTime, CreatedAt, UpdatedAt
            ) VALUES (?, ?, ?, ?, 'Paid', NOW(), NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            TotalAmount = VALUES(TotalAmount),
            PaymentMethod = VALUES(PaymentMethod),
            PaymentStatus = 'Paid',
            BillingDateTime = NOW(),
            UpdatedAt = NOW()
        ";
        $stmt = $conn->prepare($billing_query);
        $stmt->bind_param("iids", $checkin_data['ReservationID'], $checkin_id, $total_amount, $payment_method);
        $stmt->execute();
        
        $billing_id = $conn->insert_id ?: $stmt->insert_id;
        
        // Add additional charges if any
        if ($additional_charges > 0) {
            $charge_query = "
                INSERT INTO AdditionalCharges (
                    BillingID, ChargeType, Amount, ChargeDate, Description
                ) VALUES (?, 'RoomService', ?, CURDATE(), 'Additional charges at checkout')
            ";
            $stmt = $conn->prepare($charge_query);
            $stmt->bind_param("id", $billing_id, $additional_charges);
            $stmt->execute();
        }
        
        // Add late checkout charge if any
        if ($late_checkout_charge > 0) {
            $late_charge_query = "
                INSERT INTO AdditionalCharges (
                    BillingID, ChargeType, Amount, ChargeDate, Description
                ) VALUES (?, 'RoomService', ?, CURDATE(), 'Late checkout charge')
            ";
            $stmt = $conn->prepare($late_charge_query);
            $stmt->bind_param("id", $billing_id, $late_checkout_charge);
            $stmt->execute();
        }
        
        $conn->commit();
        $message = 'Guest checked out successfully! Total amount: ' . format_currency($total_amount);
        $message_type = 'success';
        
    } catch (Exception $e) {
        $conn->rollback();
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get current check-ins
$current_checkins_query = "
    SELECT ci.*, r.ReservationID, r.StartDate, r.EndDate, r.RateType,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           c.Email, c.Phone,
           rt.TypeName as RoomType, rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate,
           rm.RoomNumber,
           DATEDIFF(NOW(), ci.CheckInDateTime) as NightsStayed
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    JOIN Rooms rm ON ci.RoomID = rm.RoomID
    WHERE r.HotelID = ? AND ci.ActualCheckOutDateTime IS NULL
    ORDER BY ci.CheckInDateTime ASC
";
$stmt = $conn->prepare($current_checkins_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$current_checkins = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check-Out - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="header">
                <h1>Guest Check-Out</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>
            
            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <!-- Current Check-ins -->
                <div class="card">
                    <div class="card-header">
                        <h3>Current Guests (Checked-In)</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Room</th>
                                        <th>Guest Name</th>
                                        <th>Contact</th>
                                        <th>Room Type</th>
                                        <th>Check-in Date</th>
                                        <th>Expected Check-out</th>
                                        <th>Nights Stayed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($current_checkins->num_rows > 0): ?>
                                        <?php while ($checkin = $current_checkins->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($checkin['RoomNumber']); ?></td>
                                            <td><?php echo htmlspecialchars($checkin['CustomerName']); ?></td>
                                            <td>
                                                <?php if ($checkin['Email']): ?>
                                                    <?php echo htmlspecialchars($checkin['Email']); ?><br>
                                                <?php endif; ?>
                                                <?php if ($checkin['Phone']): ?>
                                                    <?php echo htmlspecialchars($checkin['Phone']); ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($checkin['RoomType']); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($checkin['CheckInDateTime'])); ?></td>
                                            <td><?php echo $checkin['ExpectedCheckOutDate']; ?></td>
                                            <td><?php echo $checkin['NightsStayed']; ?></td>
                                            <td>
                                                <button class="btn btn-success btn-sm" 
                                                        onclick="startCheckout(<?php echo $checkin['CheckInID']; ?>, 
                                                                              '<?php echo htmlspecialchars($checkin['CustomerName']); ?>',
                                                                              '<?php echo $checkin['RoomNumber']; ?>',
                                                                              <?php echo $checkin['NightsStayed']; ?>,
                                                                              <?php echo $checkin['DailyRate']; ?>,
                                                                              '<?php echo $checkin['RateType']; ?>',
                                                                              <?php echo $checkin['WeeklyRate'] ?? 0; ?>,
                                                                              <?php echo $checkin['MonthlyRate'] ?? 0; ?>)">
                                                    <i class="fa fa-sign-out"></i> Check-Out
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No guests currently checked in</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div class="modal fade" id="checkoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Check-Out Guest</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" id="checkin_id" name="checkin_id">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Guest Name</label>
                                    <input type="text" id="guest_name" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Room Number</label>
                                    <input type="text" id="room_number" class="form-control" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Nights Stayed</label>
                                    <input type="number" id="nights_stayed" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Room Charges</label>
                                    <input type="text" id="room_charges" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Payment Method</label>
                                    <select name="payment_method" class="form-control" required>
                                        <option value="">Select payment method</option>
                                        <option value="Cash">Cash</option>
                                        <option value="CreditCard">Credit Card</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Additional Charges</label>
                                    <input type="number" name="additional_charges" class="form-control" 
                                           step="0.01" min="0" value="0" onchange="updateTotal()">
                                    <small class="text-muted">Restaurant, room service, etc.</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Late Checkout Charge</label>
                                    <input type="number" name="late_checkout_charge" class="form-control" 
                                           step="0.01" min="0" value="0" onchange="updateTotal()">
                                    <small class="text-muted">If applicable</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label><strong>Total Amount</strong></label>
                            <input type="text" id="total_amount" class="form-control" readonly style="font-weight: bold; font-size: 1.2em;">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fa fa-sign-out"></i> Complete Check-Out
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        let baseRoomCharges = 0;
        
        function startCheckout(checkinId, guestName, roomNumber, nightsStayed, dailyRate, rateType, weeklyRate, monthlyRate) {
            document.getElementById('checkin_id').value = checkinId;
            document.getElementById('guest_name').value = guestName;
            document.getElementById('room_number').value = roomNumber;
            document.getElementById('nights_stayed').value = nightsStayed;
            
            // Calculate room charges based on rate type
            if (rateType === 'Weekly') {
                const weeks = Math.ceil(nightsStayed / 7);
                baseRoomCharges = weeks * weeklyRate;
            } else if (rateType === 'Monthly') {
                const months = Math.ceil(nightsStayed / 30);
                baseRoomCharges = months * monthlyRate;
            } else {
                baseRoomCharges = nightsStayed * dailyRate;
            }
            
            document.getElementById('room_charges').value = 'Rs. ' + baseRoomCharges.toLocaleString();
            updateTotal();
            
            $('#checkoutModal').modal('show');
        }
        
        function updateTotal() {
            const additionalCharges = parseFloat(document.querySelector('input[name="additional_charges"]').value) || 0;
            const lateCheckoutCharge = parseFloat(document.querySelector('input[name="late_checkout_charge"]').value) || 0;
            const total = baseRoomCharges + additionalCharges + lateCheckoutCharge;
            
            document.getElementById('total_amount').value = 'Rs. ' + total.toLocaleString();
        }
    </script>
</body>
</html>
