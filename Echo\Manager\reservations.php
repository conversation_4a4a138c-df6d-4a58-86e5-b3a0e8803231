<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: login.php');
    exit();
}

// Get all reservations with customer and hotel information
$reservations_query = "
    SELECT r.*,
           CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
           c.Email as CustomerEmail,
           h.Name as HotelName,
           rt.TypeName as RoomType
    FROM Reservations r
    JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN Hotels h ON r.HotelID = h.HotelID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    ORDER BY r.CreatedAt DESC
    LIMIT 50
";
$reservations_result = $conn->query($reservations_query);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservations Management - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Reservations Management</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['employee_name']); ?>
                </div>
            </div>

            <div class="content">
                <div class="card">
                    <div class="card-header">
                        <h3>Recent Reservations</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Customer</th>
                                        <th>Hotel</th>
                                        <th>Room Type</th>
                                        <th>Check In</th>
                                        <th>Check Out</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($reservation = $reservations_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($reservation['ReservationID']); ?></td>
                                        <td><?php echo htmlspecialchars($reservation['CustomerName']); ?></td>
                                        <td><?php echo htmlspecialchars($reservation['HotelName']); ?></td>
                                        <td><?php echo htmlspecialchars($reservation['RoomType']); ?></td>
                                        <td><?php echo htmlspecialchars($reservation['StartDate']); ?></td>
                                        <td><?php echo htmlspecialchars($reservation['EndDate']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $reservation['Status'] === 'Confirmed' ? 'success' : ($reservation['Status'] === 'Canceled' ? 'danger' : 'warning'); ?>">
                                                <?php echo htmlspecialchars($reservation['Status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="editReservation(<?php echo $reservation['ReservationID']; ?>, '<?php echo htmlspecialchars($reservation['Status'], ENT_QUOTES); ?>')">
                                                <i class="fa fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="viewReservationDetails(<?php echo $reservation['ReservationID']; ?>)">
                                                <i class="fa fa-eye"></i> View Details
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Reservation Modal -->
    <div class="modal fade" id="editReservationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fa fa-edit"></i> Edit Reservation</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="editReservationForm">
                    <div class="modal-body">
                        <input type="hidden" id="editReservationId" name="reservation_id">
                        <div class="form-group">
                            <label for="editReservationStatus">Status:</label>
                            <select class="form-control" id="editReservationStatus" name="status" required>
                                <option value="Pending">Pending</option>
                                <option value="Confirmed">Confirmed</option>
                                <option value="CheckedIn">Checked In</option>
                                <option value="CheckedOut">Checked Out</option>
                                <option value="Canceled">Canceled</option>
                                <option value="NoShow">No Show</option>
                            </select>
                        </div>
                        <div id="editReservationMessage"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fa fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> Update Reservation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Reservation Details Modal -->
    <div class="modal fade" id="viewReservationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fa fa-eye"></i> Reservation Details</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="reservationDetailsContent">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading reservation details...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>

    <script>
        function editReservation(reservationId, currentStatus) {
            document.getElementById('editReservationId').value = reservationId;
            document.getElementById('editReservationStatus').value = currentStatus;
            $('#editReservationModal').modal('show');
        }

        function viewReservationDetails(reservationId) {
            $('#viewReservationModal').modal('show');

            $.ajax({
                url: 'ajax/get_reservation_details.php',
                type: 'POST',
                data: { reservation_id: reservationId },
                success: function(response) {
                    $('#reservationDetailsContent').html(response);
                },
                error: function() {
                    $('#reservationDetailsContent').html('<div class="alert alert-danger">Error loading reservation details.</div>');
                }
            });
        }

        $('#editReservationForm').on('submit', function(e) {
            e.preventDefault();

            $.ajax({
                url: 'ajax/update_reservation.php',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#editReservationMessage').html('<div class="alert alert-success">' + response.message + '</div>');
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        $('#editReservationMessage').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#editReservationMessage').html('<div class="alert alert-danger">Error updating reservation.</div>');
                }
            });
        });
    </script>
</body>
</html>
