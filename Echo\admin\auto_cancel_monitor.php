<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is admin/manager
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['user_type'], ['admin', 'manager'])) {
    header('Location: ../auth/login.php');
    exit();
}

// Set timezone
date_default_timezone_set('Asia/Colombo');

$page_title = "Auto-Cancellation Monitor";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Echo Hotels</title>
    
    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css?family=Lora:400,700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Cabin:400,500,600,700&display=swap" rel="stylesheet">
    
    <!-- Css Styles -->
    <link rel="stylesheet" href="../css/bootstrap.min.css" type="text/css">
    <link rel="stylesheet" href="../css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="../css/elegant-icons.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css" type="text/css">
    
    <style>
        .dashboard-section {
            padding: 60px 0;
            background: #f8f9fa;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #dfa974 0%, #c8956d 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #28a745 0%, #218838 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .table-responsive {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-confirmed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .status-no-show { background: #fff3cd; color: #856404; }
        
        .log-viewer {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #dfa974;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 50%;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .refresh-btn:hover {
            background: #c8956d;
        }
    </style>
</head>

<body>
    <!-- Header Section Begin -->
    <header class="header-section">
        <div class="top-nav">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <ul class="tn-left">
                            <li><i class="fa fa-phone"></i> (12) 345 67890</li>
                            <li><i class="fa fa-envelope"></i> <EMAIL></li>
                        </ul>
                    </div>
                    <div class="col-lg-6">
                        <div class="tn-right">
                            <a href="../profile/<?php echo $_SESSION['user_type']; ?>.php" class="bk-btn">Back to Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="menu-item">
            <div class="container">
                <div class="row">
                    <div class="col-lg-2">
                        <div class="logo">
                            <a href="../index.php">
                                <img src="../img/logo.png" alt="">
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-10">
                        <div class="nav-menu">
                            <nav class="mainmenu">
                                <ul>
                                    <li><a href="../index.php">Home</a></li>
                                    <li><a href="../profile/<?php echo $_SESSION['user_type']; ?>.php">Dashboard</a></li>
                                    <li><a href="../auth/logout.php">Logout</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header End -->

    <!-- Dashboard Section Begin -->
    <section class="dashboard-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>System Monitoring</span>
                        <h2>Auto-Cancellation Monitor</h2>
                        <p>Real-time monitoring of automatic reservation cancellations</p>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <?php
                // Get statistics
                $current_time = date('Y-m-d H:i:s');
                $current_hour = (int)date('H');
                $today = date('Y-m-d');
                
                // Count reservations without credit card
                $without_cc_query = "
                    SELECT COUNT(*) as count 
                    FROM Reservations r 
                    WHERE r.Status = 'Confirmed' 
                    AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '') 
                    AND r.CustomerID IS NOT NULL 
                    AND r.CompanyID IS NULL
                ";
                $without_cc_result = $conn->query($without_cc_query);
                $without_cc_count = $without_cc_result->fetch_assoc()['count'];
                
                // Count eligible for cancellation
                $eligible_query = "
                    SELECT COUNT(*) as count 
                    FROM Reservations r 
                    WHERE r.Status = 'Confirmed' 
                    AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '') 
                    AND r.CustomerID IS NOT NULL 
                    AND r.CompanyID IS NULL
                    AND (
                        (DATE(r.CreatedAt) = CURDATE() AND HOUR(NOW()) >= 19) 
                        OR DATE(r.CreatedAt) < CURDATE()
                    )
                ";
                $eligible_result = $conn->query($eligible_query);
                $eligible_count = $eligible_result->fetch_assoc()['count'];
                
                // Count cancelled today
                $cancelled_today_query = "
                    SELECT COUNT(*) as count 
                    FROM Reservations r 
                    WHERE r.Status = 'Cancelled' 
                    AND DATE(r.UpdatedAt) = CURDATE()
                    AND r.CancellationReason LIKE '%Auto-cancelled%'
                ";
                $cancelled_today_result = $conn->query($cancelled_today_query);
                $cancelled_today_count = $cancelled_today_result->fetch_assoc()['count'];
                
                // Count no-shows today
                $no_show_today_query = "
                    SELECT COUNT(*) as count 
                    FROM Reservations r 
                    WHERE r.Status = 'No-Show' 
                    AND DATE(r.UpdatedAt) = CURDATE()
                ";
                $no_show_today_result = $conn->query($no_show_today_query);
                $no_show_today_count = $no_show_today_result->fetch_assoc()['count'];
                ?>
                
                <div class="stat-card info">
                    <div class="stat-number"><?php echo $without_cc_count; ?></div>
                    <div class="stat-label">Reservations Without Credit Card</div>
                </div>
                
                <div class="stat-card <?php echo $eligible_count > 0 ? 'danger' : 'success'; ?>">
                    <div class="stat-number"><?php echo $eligible_count; ?></div>
                    <div class="stat-label">Eligible for Auto-Cancellation</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number"><?php echo $cancelled_today_count; ?></div>
                    <div class="stat-label">Auto-Cancelled Today</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number"><?php echo $no_show_today_count; ?></div>
                    <div class="stat-label">No-Shows Today</div>
                </div>
            </div>

            <!-- System Status -->
            <div class="dashboard-card">
                <h4><i class="fa fa-clock-o"></i> System Status</h4>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Current Time:</strong> <?php echo $current_time; ?></p>
                        <p><strong>Current Hour:</strong> <?php echo $current_hour; ?>:00</p>
                        <?php if ($current_hour >= 19): ?>
                            <p class="text-success"><i class="fa fa-check-circle"></i> <strong>Auto-cancellation is ACTIVE</strong></p>
                        <?php else: ?>
                            <p class="text-warning"><i class="fa fa-clock-o"></i> <strong>Auto-cancellation activates at 7:00 PM</strong></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Next Cleanup:</strong> Today at 7:00 PM</p>
                        <p><strong>Policy:</strong> Cancel reservations without credit card at 7 PM daily</p>
                        <p><strong>No-Show Fee:</strong> LKR 1,500.00</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Dashboard Section End -->

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="location.reload()" title="Refresh Data">
        <i class="fa fa-refresh"></i>
    </button>

    <!-- Js Plugins -->
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    
    <script>
        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
        
        // Update time every second
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                timeZone: 'Asia/Colombo',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            // Update any time displays if needed
        }, 1000);
    </script>
</body>
</html>
