/* Admin Dashboard Styles - Clerk Purple Gradient Scheme */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

:root {
    --primary-color: #667eea;       /* Primary purple */
    --secondary-color: #764ba2;     /* Secondary purple */
    --accent-color: #5a6fd8;        /* Light purple */
    --success-color: #56ab2f;       /* Green for success */
    --success-light: #a8e6cf;      /* Light green */
    --warning-color: #f093fb;      /* Pink for warnings */
    --warning-light: #f5576c;      /* Light pink */
    --info-color: #4facfe;         /* Blue for info */
    --info-light: #00f2fe;         /* Light blue */
    --danger-color: #dc3545;       /* Red for danger */
    --danger-light: #c82333;       /* Light red */
    --text-color: #333333;         /* Dark text */
    --light-bg: #f8f9fa;           /* Light background */
    --border-color: #dee2e6;       /* Light border */
    --chart-color-1: #667eea;      /* Primary purple */
    --chart-color-2: #764ba2;      /* Secondary purple */
    --chart-color-3: #56ab2f;      /* Green */
    --chart-color-4: #4facfe;      /* Blue */
}

.main-content {
    margin-left: 250px !important;
    flex: 1;
    padding: 0;
    width: calc(100% - 250px);
    min-height: 100vh;
    background-color: var(--light-bg);
    overflow-x: hidden;
}

.header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
}

.content {
    padding: 30px;
}

.header h1 {
    margin: 0;
    color: #333;
    font-size: 1.8rem;
}

.hotel-selector {
    padding: 8px 15px;
    border: 2px solid var(--primary-color);
    border-radius: 5px;
    font-size: 14px;
    margin-right: 10px;
    background-color: white;
    color: var(--secondary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, var(--accent-color) 0%, #6a4190 100%);
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.action-btn i {
    font-size: 14px;
}

.refresh-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, var(--accent-color) 0%, #6a4190 100%);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.metric-card h3 {
    color: var(--secondary-color);
    font-size: 18px;
    margin-bottom: 15px;
}

.room-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    text-align: center;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    display: block;
}

.metric-label {
    font-size: 12px;
    color: var(--text-color);
    display: block;
    margin-top: 5px;
}

.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: var(--secondary-color);
}

.bottom-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: none;
    transition: all 0.3s ease;
}

.table-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.table-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: bold;
}

.table-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.hotel-rooms-status {
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.hotel-rooms-status:last-child {
    border-bottom: none;
}

.hotel-rooms-status h4 {
    color: var(--secondary-color);
    font-size: 16px;
    margin-bottom: 10px;
}

.room-type-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.room-type-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: linear-gradient(135deg, var(--light-bg) 0%, rgba(154, 203, 208, 0.1) 100%);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.room-type-item:hover {
    background: linear-gradient(135deg, rgba(154, 203, 208, 0.1) 0%, rgba(72, 166, 167, 0.1) 100%);
    transform: translateX(5px);
}

.room-type-name {
    color: var(--secondary-color);
    font-size: 14px;
}

.room-type-count {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 14px;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item strong {
    color: var(--secondary-color);
    font-size: 14px;
}

.activity-item small {
    color: var(--text-color);
    font-size: 12px;
}

.activity-time {
    color: var(--text-color);
    font-size: 12px;
}

@media (max-width: 1200px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .main-content {
        margin-left: 0;
    }

    .bottom-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
        gap: 10px;
    }

    .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .hotel-selector {
        width: 100%;
        margin-right: 0;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }
}

/* Clerk Dashboard Style Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-color) 0%, #6a4190 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #4e9a2a 0%, #95d3bc 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #545b62 100%);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0 !important;
    padding: 20px;
    font-weight: 600;
    color: #333;
}

/* Badge Styling */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 6px 12px;
}

.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%) !important;
    color: white !important;
}

.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-light) 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-light) 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

/* Table Enhancements */
.table-dark {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(102, 126, 234, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

/* Form Controls */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Remove Scrollbar */
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: transparent;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* Action Buttons Styling */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    font-weight: 500;
    margin: 2px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-sm i {
    font-size: 11px;
}

/* Edit Button - Purple */
.btn-primary.btn-sm {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
}

.btn-primary.btn-sm:hover {
    background: linear-gradient(135deg, var(--accent-color) 0%, #6a4190 100%);
    color: white;
}

/* View/Info Button - Teal */
.btn-info.btn-sm {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    color: white;
}

.btn-info.btn-sm:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    color: white;
}

/* Action buttons container */
td .btn-sm {
    min-width: 80px;
    justify-content: center;
}

/* Modal enhancements */
.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-footer .btn {
    min-width: 100px;
}