<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];

// Get billing records for this hotel
$billing_query = "
    SELECT b.*,
           r.ReservationID, r.StartDate, r.EndDate,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           c.Email,
           rm.RoomNumber,
           rt.TypeName as RoomType,
           ci.CheckInDateTime, ci.ActualCheckOutDateTime,
           (SELECT SUM(ac.Amount) FROM AdditionalCharges ac WHERE ac.BillingID = b.BillingID) as AdditionalChargesTotal
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    LEFT JOIN CheckIns ci ON b.CheckInID = ci.CheckInID
    LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.HotelID = ?
    ORDER BY b.BillingDateTime DESC
    LIMIT 50
";
$stmt = $conn->prepare($billing_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$billing_result = $stmt->get_result();

// Get summary statistics
$stats_query = "
    SELECT
        COUNT(*) as TotalBills,
        SUM(CASE WHEN PaymentStatus = 'Paid' THEN TotalAmount ELSE 0 END) as PaidAmount,
        SUM(CASE WHEN PaymentStatus = 'Pending' THEN TotalAmount ELSE 0 END) as PendingAmount,
        SUM(TotalAmount) as TotalAmount
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    WHERE r.HotelID = ? AND DATE(b.BillingDateTime) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
";
$stmt = $conn->prepare($stats_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Billing - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Billing Management</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card primary">
                            <h3><?php echo $stats['TotalBills']; ?></h3>
                            <p>Total Bills (30 days)</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card success">
                            <h3><?php echo format_currency($stats['PaidAmount']); ?></h3>
                            <p>Paid Amount</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card warning">
                            <h3><?php echo format_currency($stats['PendingAmount']); ?></h3>
                            <p>Pending Amount</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card info">
                            <h3><?php echo format_currency($stats['TotalAmount']); ?></h3>
                            <p>Total Revenue</p>
                        </div>
                    </div>
                </div>

                <!-- Billing Records -->
                <div class="card">
                    <div class="card-header">
                        <h3>Recent Billing Records</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Bill ID</th>
                                        <th>Guest</th>
                                        <th>Room</th>
                                        <th>Stay Period</th>
                                        <th>Room Charges</th>
                                        <th>Additional</th>
                                        <th>Total Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($billing_result->num_rows > 0): ?>
                                        <?php while ($bill = $billing_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $bill['BillingID']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($bill['CustomerName']); ?>
                                                <?php if ($bill['Email']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($bill['Email']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo $bill['RoomNumber'] ?? 'N/A'; ?>
                                                <?php if ($bill['RoomType']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($bill['RoomType']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo $bill['StartDate']; ?> to<br><?php echo $bill['EndDate']; ?>
                                                <?php if ($bill['CheckInDateTime'] && $bill['ActualCheckOutDateTime']): ?>
                                                    <br><small class="text-muted">
                                                        Actual: <?php echo date('M d', strtotime($bill['CheckInDateTime'])); ?> -
                                                        <?php echo date('M d', strtotime($bill['ActualCheckOutDateTime'])); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $room_charges = $bill['TotalAmount'] - ($bill['AdditionalChargesTotal'] ?? 0);
                                                echo format_currency($room_charges);
                                                ?>
                                            </td>
                                            <td><?php echo format_currency($bill['AdditionalChargesTotal'] ?? 0); ?></td>
                                            <td><strong><?php echo format_currency($bill['TotalAmount']); ?></strong></td>
                                            <td>
                                                <span class="badge badge-<?php
                                                    echo $bill['PaymentMethod'] === 'Cash' ? 'success' :
                                                        ($bill['PaymentMethod'] === 'CreditCard' ? 'primary' :
                                                        ($bill['PaymentMethod'] === 'No-Show Charge' ? 'danger' : 'info'));
                                                ?>">
                                                    <?php echo htmlspecialchars($bill['PaymentMethod']); ?>
                                                </span>
                                                <?php if ($bill['PaymentMethod'] === 'No-Show Charge'): ?>
                                                    <br><small class="text-danger">No-Show Fee</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php
                                                    echo $bill['PaymentStatus'] === 'Paid' ? 'success' :
                                                        ($bill['PaymentStatus'] === 'Pending' ? 'warning' :
                                                        ($bill['PaymentStatus'] === 'No-Show' ? 'danger' : 'secondary'));
                                                ?>">
                                                    <?php echo htmlspecialchars($bill['PaymentStatus']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M d, Y H:i', strtotime($bill['BillingDateTime'])); ?></td>
                                            <td>
                                                <button class="btn btn-info btn-sm"
                                                        onclick="viewBillDetails(<?php echo $bill['BillingID']; ?>)">
                                                    <i class="fa fa-eye"></i> View
                                                </button>
                                                <button class="btn btn-primary btn-sm"
                                                        onclick="printBill(<?php echo $bill['BillingID']; ?>)">
                                                    <i class="fa fa-print"></i> Print
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="11" class="text-center">No billing records found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bill Details Modal -->
    <div class="modal fade" id="billDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bill Details</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="billDetailsContent">
                    <!-- Bill details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="printCurrentBill()">
                        <i class="fa fa-print"></i> Print Bill
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        let currentBillId = null;

        function viewBillDetails(billId) {
            currentBillId = billId;

            // Load bill details via AJAX
            $.ajax({
                url: 'ajax/get_bill_details.php',
                method: 'POST',
                data: { bill_id: billId },
                success: function(response) {
                    $('#billDetailsContent').html(response);
                    $('#billDetailsModal').modal('show');
                },
                error: function() {
                    alert('Error loading bill details');
                }
            });
        }

        function printBill(billId) {
            window.open('print_bill.php?bill_id=' + billId, '_blank');
        }

        function printCurrentBill() {
            if (currentBillId) {
                printBill(currentBillId);
            }
        }
    </script>
</body>
</html>
