<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: login.php');
    exit();
}

// Get hotel filter from URL if provided
$hotel_filter = isset($_GET['hotel_id']) ? intval($_GET['hotel_id']) : 0;
$hotel_name_filter = isset($_GET['hotel_name']) ? $_GET['hotel_name'] : '';

// Build query based on filter
if ($hotel_filter > 0) {
    $rooms_query = "
        SELECT r.*, h.Name as HotelName, rt.TypeName, rt.DailyRate
        FROM Rooms r
        JOIN Hotels h ON r.HotelID = h.HotelID
        JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        WHERE r.HotelID = ?
        ORDER BY r.RoomNumber
    ";
    $stmt = $conn->prepare($rooms_query);
    $stmt->bind_param("i", $hotel_filter);
    $stmt->execute();
    $rooms_result = $stmt->get_result();
} else {
    $rooms_query = "
        SELECT r.*, h.Name as HotelName, rt.TypeName, rt.DailyRate
        FROM Rooms r
        JOIN Hotels h ON r.HotelID = h.HotelID
        JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        ORDER BY h.Name, r.RoomNumber
    ";
    $rooms_result = $conn->query($rooms_query);
}

// Get all hotels for the filter dropdown
$hotels_query = "SELECT HotelID, Name FROM Hotels ORDER BY Name";
$hotels_result = $conn->query($hotels_query);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rooms Management - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Rooms Management<?php echo $hotel_name_filter ? ' - ' . htmlspecialchars($hotel_name_filter) : ''; ?></h1>
                <div class="header-actions">
                    <select class="hotel-selector" id="hotelFilter" onchange="filterByHotel()">
                        <option value="0">All Hotels</option>
                        <?php while ($hotel = $hotels_result->fetch_assoc()): ?>
                            <option value="<?php echo $hotel['HotelID']; ?>" <?php echo $hotel['HotelID'] == $hotel_filter ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($hotel['Name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                    <div class="user-info">
                        Welcome, <?php echo htmlspecialchars($_SESSION['employee_name']); ?>
                    </div>
                </div>
            </div>

            <div class="content">
                <div class="card">
                    <div class="card-header">
                        <h3>All Rooms</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Room ID</th>
                                        <th>Hotel</th>
                                        <th>Room Number</th>
                                        <th>Type</th>
                                        <th>Daily Rate</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($room = $rooms_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($room['RoomID']); ?></td>
                                        <td><?php echo htmlspecialchars($room['HotelName']); ?></td>
                                        <td><?php echo htmlspecialchars($room['RoomNumber']); ?></td>
                                        <td><?php echo htmlspecialchars($room['TypeName']); ?></td>
                                        <td><?php echo format_currency($room['DailyRate']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $room['Status'] === 'Available' ? 'success' : ($room['Status'] === 'Occupied' ? 'warning' : 'danger'); ?>">
                                                <?php echo htmlspecialchars($room['Status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="editRoom(<?php echo $room['RoomID']; ?>, '<?php echo htmlspecialchars($room['RoomNumber'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($room['Status'], ENT_QUOTES); ?>')">
                                                <i class="fa fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="viewRoomDetails(<?php echo $room['RoomID']; ?>, '<?php echo htmlspecialchars($room['RoomNumber'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($room['HotelName'], ENT_QUOTES); ?>')">
                                                <i class="fa fa-eye"></i> View Details
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Room Modal -->
    <div class="modal fade" id="editRoomModal" tabindex="-1" role="dialog" aria-labelledby="editRoomModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRoomModalLabel">
                        <i class="fa fa-edit"></i> Edit Room
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="editRoomForm" method="POST" action="update_room.php">
                    <div class="modal-body">
                        <input type="hidden" id="editRoomId" name="room_id">
                        <div class="form-group">
                            <label for="editRoomNumber">Room Number</label>
                            <input type="text" class="form-control" id="editRoomNumber" name="room_number" required>
                        </div>
                        <div class="form-group">
                            <label for="editRoomStatus">Status</label>
                            <select class="form-control" id="editRoomStatus" name="room_status" required>
                                <option value="Available">Available</option>
                                <option value="Occupied">Occupied</option>
                                <option value="Maintenance">Maintenance</option>
                                <option value="Out of Order">Out of Order</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">
                            <i class="fa fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Room Details Modal -->
    <div class="modal fade" id="roomDetailsModal" tabindex="-1" role="dialog" aria-labelledby="roomDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="roomDetailsModalLabel">
                        <i class="fa fa-eye"></i> Room Details
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="roomDetailsContent">
                    <!-- Content will be loaded via AJAX -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>

    <script>
        // Filter by hotel function
        function filterByHotel() {
            const hotelId = document.getElementById('hotelFilter').value;
            if (hotelId == 0) {
                window.location.href = 'rooms.php';
            } else {
                const selectedOption = document.getElementById('hotelFilter').selectedOptions[0];
                const hotelName = selectedOption.text;
                window.location.href = 'rooms.php?hotel_id=' + hotelId + '&hotel_name=' + encodeURIComponent(hotelName);
            }
        }

        // Edit room function
        function editRoom(roomId, roomNumber, roomStatus) {
            document.getElementById('editRoomId').value = roomId;
            document.getElementById('editRoomNumber').value = roomNumber;
            document.getElementById('editRoomStatus').value = roomStatus;
            $('#editRoomModal').modal('show');
        }

        // View room details function
        function viewRoomDetails(roomId, roomNumber, hotelName) {
            // Load room details via AJAX
            $.ajax({
                url: 'ajax/get_room_details.php',
                method: 'POST',
                data: { room_id: roomId },
                success: function(response) {
                    $('#roomDetailsContent').html(response);
                    $('#roomDetailsModal').modal('show');
                },
                error: function() {
                    alert('Error loading room details');
                }
            });
        }

        // Handle form submission
        document.getElementById('editRoomForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('update_room.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Room updated successfully!');
                    $('#editRoomModal').modal('hide');
                    location.reload(); // Refresh the page to show updated data
                } else {
                    alert('Error updating room: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the room.');
            });
        });
    </script>
</body>
</html>
