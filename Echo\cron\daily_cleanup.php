<?php
/**
 * Daily Cleanup Script for Echo Hotel Management System
 *
 * This script should be run daily at 7:00 PM via cron job
 *
 * Functions:
 * 1. Cancel reservations without credit card details
 * 2. Create billing records for no-show customers
 *
 * Cron job command (add to crontab):
 * 0 19 * * * /usr/bin/php /path/to/your/project/cron/daily_cleanup.php
 */

require_once(__DIR__ . '/../config/db.php');

// Set timezone
date_default_timezone_set('Asia/Colombo'); // Sri Lanka timezone

// Log file for tracking cleanup activities
$log_file = __DIR__ . '/cleanup_log.txt';

function writeLog($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

function cancelReservationsWithoutCreditCard($conn) {
    writeLog("Starting cancellation of reservations without credit card details...");

    try {
        // Find reservations without credit card details that are still active
        // Only cancel customer reservations (not travel agent bookings)
        $query = "
            SELECT r.ReservationID, r.CustomerID, r.HotelID, r.RoomTypeID,
                   r.StartDate, r.EndDate, r.CreatedAt, h.Name as HotelName,
                   rt.TypeName as RoomType, c.FirstName, c.LastName, c.Email
            FROM Reservations r
            LEFT JOIN Hotels h ON r.HotelID = h.HotelID
            LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            WHERE r.Status = 'Confirmed'
            AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '')
            AND r.CustomerID IS NOT NULL
            AND r.CompanyID IS NULL
            AND r.CreatedAt <= DATE_SUB(NOW(), INTERVAL 0 DAY)
        ";

        $result = $conn->query($query);
        $cancelled_count = 0;

        if ($result && $result->num_rows > 0) {
            while ($reservation = $result->fetch_assoc()) {
                // Check if reservation was created today and it's past 7 PM
                $created_date = date('Y-m-d', strtotime($reservation['CreatedAt']));
                $today = date('Y-m-d');
                $current_hour = (int)date('H');

                // Cancel if:
                // 1. Reservation was created today and it's past 7 PM (19:00)
                // 2. Reservation was created before today
                if (($created_date === $today && $current_hour >= 19) || $created_date < $today) {

                    // Update reservation status to cancelled
                    $update_query = "
                        UPDATE Reservations
                        SET Status = 'Cancelled',
                            UpdatedAt = NOW(),
                            CancellationReason = 'Auto-cancelled: No credit card details provided by 7 PM deadline'
                        WHERE ReservationID = ?
                    ";

                    $stmt = $conn->prepare($update_query);
                    $stmt->bind_param("i", $reservation['ReservationID']);

                    if ($stmt->execute()) {
                        $cancelled_count++;
                        writeLog("Cancelled reservation #{$reservation['ReservationID']} for customer {$reservation['FirstName']} {$reservation['LastName']} - {$reservation['HotelName']} {$reservation['RoomType']} (Created: {$reservation['CreatedAt']})");

                        // Update any existing billing records to cancelled
                        $update_billing_query = "
                            UPDATE Billing
                            SET PaymentStatus = 'Cancelled', UpdatedAt = NOW()
                            WHERE ReservationID = ? AND PaymentStatus = 'Pending'
                        ";
                        $billing_stmt = $conn->prepare($update_billing_query);
                        $billing_stmt->bind_param("i", $reservation['ReservationID']);
                        $billing_stmt->execute();

                        // Optional: Send cancellation email notification
                        sendCancellationEmail($reservation);
                    } else {
                        writeLog("ERROR: Failed to cancel reservation #{$reservation['ReservationID']}");
                    }
                }
            }
        }

        writeLog("Completed cancellation process. Total cancelled: $cancelled_count reservations");
        return $cancelled_count;

    } catch (Exception $e) {
        writeLog("ERROR in cancelReservationsWithoutCreditCard: " . $e->getMessage());
        return 0;
    }
}

function createNoShowBillingRecords($conn) {
    writeLog("Starting creation of no-show billing records...");

    try {
        // Find reservations where check-in date has passed but customer didn't show up
        // Include both customer and travel agent reservations
        $query = "
            SELECT r.ReservationID, r.CustomerID, r.CompanyID, r.HotelID, r.RoomTypeID,
                   r.StartDate, r.EndDate, r.RateType, r.DiscountedRate,
                   rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate,
                   h.Name as HotelName, rt.TypeName as RoomType,
                   c.FirstName, c.LastName, c.Email,
                   tc.CompanyName, tc.ContactEmail,
                   b.BillingID, b.PaymentStatus
            FROM Reservations r
            LEFT JOIN Hotels h ON r.HotelID = h.HotelID
            LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            LEFT JOIN TravelCompanies tc ON r.CompanyID = tc.CompanyID
            LEFT JOIN Billing b ON r.ReservationID = b.ReservationID
            WHERE r.Status = 'Confirmed'
            AND r.StartDate < CURDATE()
            AND (r.CustomerID IS NOT NULL OR r.CompanyID IS NOT NULL)
            AND (b.BillingID IS NULL OR b.PaymentStatus = 'Pending')
        ";

        $result = $conn->query($query);
        $billing_count = 0;

        if ($result && $result->num_rows > 0) {
            while ($reservation = $result->fetch_assoc()) {
                // Fixed no-show charge of 1500 LKR as per hotel policy
                $total_amount = 1500.00;

                // Determine customer/company info for logging
                $customer_info = '';
                if ($reservation['CustomerID']) {
                    $customer_info = "{$reservation['FirstName']} {$reservation['LastName']} ({$reservation['Email']})";
                } else {
                    $customer_info = "{$reservation['CompanyName']} ({$reservation['ContactEmail']})";
                }

                if ($reservation['BillingID']) {
                    // Update existing billing record
                    $update_billing_query = "
                        UPDATE Billing
                        SET PaymentStatus = 'No-Show',
                            TotalAmount = ?,
                            PaymentMethod = 'No-Show Charge',
                            BillingDateTime = NOW(),
                            UpdatedAt = NOW()
                        WHERE BillingID = ?
                    ";

                    $stmt = $conn->prepare($update_billing_query);
                    $stmt->bind_param("di", $total_amount, $reservation['BillingID']);
                } else {
                    // Create new billing record for no-show
                    $insert_billing_query = "
                        INSERT INTO Billing (
                            ReservationID, TotalAmount, PaymentMethod, PaymentStatus,
                            BillingDateTime, CreatedAt, UpdatedAt
                        ) VALUES (?, ?, 'No-Show Charge', 'No-Show', NOW(), NOW(), NOW())
                    ";

                    $stmt = $conn->prepare($insert_billing_query);
                    $stmt->bind_param("id", $reservation['ReservationID'], $total_amount);
                }

                if ($stmt->execute()) {
                    $billing_count++;
                    writeLog("Created no-show billing for reservation #{$reservation['ReservationID']} - $customer_info - Amount: LKR " . number_format($total_amount, 2) . " ({$reservation['RateType']} rate)");

                    // Update reservation status to no-show
                    $update_reservation_query = "
                        UPDATE Reservations
                        SET Status = 'No-Show', UpdatedAt = NOW()
                        WHERE ReservationID = ?
                    ";

                    $stmt2 = $conn->prepare($update_reservation_query);
                    $stmt2->bind_param("i", $reservation['ReservationID']);
                    $stmt2->execute();

                    // Send no-show notification email
                    sendNoShowNotification($reservation, $total_amount);

                } else {
                    writeLog("ERROR: Failed to create no-show billing for reservation #{$reservation['ReservationID']}");
                }
            }
        }

        writeLog("Completed no-show billing process. Total billing records created: $billing_count");
        return $billing_count;

    } catch (Exception $e) {
        writeLog("ERROR in createNoShowBillingRecords: " . $e->getMessage());
        return 0;
    }
}

function sendCancellationEmail($reservation) {
    // Email notification for cancelled reservations
    if (empty($reservation['Email'])) {
        writeLog("No email address available for reservation #{$reservation['ReservationID']}");
        return;
    }

    $to = $reservation['Email'];
    $subject = "Reservation Cancelled - Echo Hotels";
    $message = "
Dear {$reservation['FirstName']} {$reservation['LastName']},

Your reservation has been automatically cancelled due to missing credit card details.

Reservation Details:
- Reservation ID: #{$reservation['ReservationID']}
- Hotel: {$reservation['HotelName']}
- Room Type: {$reservation['RoomType']}
- Check-in Date: {$reservation['StartDate']}
- Check-out Date: {$reservation['EndDate']}

Reason: No credit card details provided by 7:00 PM deadline.

To make a new reservation with guaranteed booking, please visit our website
and provide credit card details during the booking process.

Thank you for your understanding.

Best regards,
Echo Hotels Management Team
    ";

    // Uncomment the line below to enable email notifications
    // mail($to, $subject, $message);

    writeLog("Cancellation email notification prepared for {$reservation['Email']} (Reservation #{$reservation['ReservationID']})");
}

function sendNoShowNotification($reservation, $total_amount) {
    // Email notification for no-show billing
    $email = '';
    $name = '';

    if ($reservation['CustomerID']) {
        $email = $reservation['Email'];
        $name = "{$reservation['FirstName']} {$reservation['LastName']}";
    } else {
        $email = $reservation['ContactEmail'];
        $name = $reservation['CompanyName'];
    }

    if (empty($email)) {
        writeLog("No email address available for no-show notification (Reservation #{$reservation['ReservationID']})");
        return;
    }

    $subject = "No-Show Billing Notice - Echo Hotels";
    $message = "
Dear $name,

This is to inform you that a billing record has been created for a no-show reservation.

Reservation Details:
- Reservation ID: #{$reservation['ReservationID']}
- Hotel: {$reservation['HotelName']}
- Room Type: {$reservation['RoomType']}
- Check-in Date: {$reservation['StartDate']}
- Check-out Date: {$reservation['EndDate']}
- Rate Type: {$reservation['RateType']}

Billing Information:
- Amount Due: LKR " . number_format($total_amount, 2) . "
- Billing Date: " . date('Y-m-d H:i:s') . "
- Payment Status: No-Show Charge

As per our policy, customers who fail to show up for their confirmed reservations
are charged a no-show fee of LKR 1,500.00.

Please contact our billing department if you have any questions or concerns.

Best regards,
Echo Hotels Billing Department
    ";

    // Uncomment the line below to enable email notifications
    // mail($email, $subject, $message);

    writeLog("No-show billing notification prepared for $email (Reservation #{$reservation['ReservationID']}, Amount: LKR " . number_format($total_amount, 2) . ")");
}

// Main execution
try {
    writeLog("=== Daily Cleanup Script Started ===");

    // 1. Cancel reservations without credit card details
    $cancelled_reservations = cancelReservationsWithoutCreditCard($conn);

    // 2. Create billing records for no-show customers
    $no_show_billings = createNoShowBillingRecords($conn);

    writeLog("=== Daily Cleanup Script Completed ===");
    writeLog("Summary: $cancelled_reservations reservations cancelled, $no_show_billings no-show billing records created");

    // Output summary for cron job logs
    echo "Daily cleanup completed successfully.\n";
    echo "Cancelled reservations: $cancelled_reservations\n";
    echo "No-show billing records: $no_show_billings\n";

} catch (Exception $e) {
    writeLog("FATAL ERROR: " . $e->getMessage());
    echo "Error occurred during daily cleanup: " . $e->getMessage() . "\n";
    exit(1);
}

// Close database connection
$conn->close();
?>
