<?php
session_start();
require_once('../config/db.php');

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get form data
$room_id = isset($_POST['room_id']) ? intval($_POST['room_id']) : 0;
$room_number = isset($_POST['room_number']) ? trim($_POST['room_number']) : '';
$room_status = isset($_POST['room_status']) ? trim($_POST['room_status']) : '';

// Validate input
if ($room_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid room ID']);
    exit();
}

if (empty($room_number)) {
    echo json_encode(['success' => false, 'message' => 'Room number is required']);
    exit();
}

if (empty($room_status)) {
    echo json_encode(['success' => false, 'message' => 'Room status is required']);
    exit();
}

// Validate status
$valid_statuses = ['Available', 'Occupied', 'Maintenance', 'Out of Order'];
if (!in_array($room_status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid room status']);
    exit();
}

try {
    // Check if room exists
    $check_query = "SELECT RoomID, HotelID FROM Rooms WHERE RoomID = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $room_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Room not found']);
        exit();
    }
    
    $room_data = $result->fetch_assoc();
    $hotel_id = $room_data['HotelID'];
    
    // Check if another room with the same number exists in the same hotel (excluding current room)
    $number_check_query = "SELECT RoomID FROM Rooms WHERE RoomNumber = ? AND HotelID = ? AND RoomID != ?";
    $number_check_stmt = $conn->prepare($number_check_query);
    $number_check_stmt->bind_param("sii", $room_number, $hotel_id, $room_id);
    $number_check_stmt->execute();
    $number_result = $number_check_stmt->get_result();
    
    if ($number_result->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'A room with this number already exists in this hotel']);
        exit();
    }
    
    // Update room
    $update_query = "UPDATE Rooms SET RoomNumber = ?, Status = ? WHERE RoomID = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssi", $room_number, $room_status, $room_id);
    
    if ($update_stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Room updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update room']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}

$conn->close();
?>
