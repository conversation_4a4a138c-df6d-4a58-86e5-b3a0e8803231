<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $company_name = trim($_POST['company_name']);
    $contact_email = trim($_POST['contact_email']);
    $contact_phone = trim($_POST['contact_phone']);
    $address = trim($_POST['address']);
    
    // Validate input
    if (empty($company_name) || empty($contact_email) || empty($contact_phone)) {
        echo json_encode(['success' => false, 'message' => 'Company name, contact email, and contact phone are required']);
        exit();
    }
    
    // Validate email format
    if (!filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit();
    }
    
    try {
        // Check if company name already exists
        $check_query = "SELECT CompanyID FROM TravelCompanies WHERE CompanyName = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("s", $company_name);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Company name already exists']);
            exit();
        }
        
        // Check if contact email already exists
        $check_email_query = "SELECT CompanyID FROM TravelCompanies WHERE ContactEmail = ?";
        $check_email_stmt = $conn->prepare($check_email_query);
        $check_email_stmt->bind_param("s", $contact_email);
        $check_email_stmt->execute();
        $check_email_result = $check_email_stmt->get_result();
        
        if ($check_email_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Contact email already exists']);
            exit();
        }
        
        // Insert new company
        $insert_query = "INSERT INTO TravelCompanies (CompanyName, ContactEmail, ContactPhone, Address, CreatedAt) VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("ssss", $company_name, $contact_email, $contact_phone, $address);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Travel company added successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Error adding company: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
