<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reservation_id = intval($_POST['reservation_id']);
    $status = trim($_POST['status']);
    
    // Validate input
    if (empty($reservation_id) || empty($status)) {
        echo json_encode(['success' => false, 'message' => 'All fields are required']);
        exit();
    }
    
    // Validate status
    $valid_statuses = ['Pending', 'Confirmed', 'CheckedIn', 'CheckedOut', 'Canceled', 'NoShow'];
    if (!in_array($status, $valid_statuses)) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit();
    }
    
    try {
        // Update reservation
        $update_query = "UPDATE Reservations SET Status = ?, UpdatedAt = NOW() WHERE ReservationID = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("si", $status, $reservation_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => 'Reservation updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Reservation not found or no changes made']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Error updating reservation: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
