<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $password = trim($_POST['password']);
    $role = trim($_POST['role']);
    $hotel_id = !empty($_POST['hotel_id']) ? intval($_POST['hotel_id']) : null;
    
    // Validate input
    if (empty($first_name) || empty($last_name) || empty($email) || empty($password) || empty($role)) {
        echo json_encode(['success' => false, 'message' => 'All fields except hotel are required']);
        exit();
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit();
    }
    
    // Validate role
    $valid_roles = ['Admin', 'Manager', 'Clerk'];
    if (!in_array($role, $valid_roles)) {
        echo json_encode(['success' => false, 'message' => 'Invalid role']);
        exit();
    }
    
    // Validate hotel assignment
    if ($role !== 'Admin' && empty($hotel_id)) {
        echo json_encode(['success' => false, 'message' => 'Non-admin employees must be assigned to a hotel']);
        exit();
    }
    
    if ($role === 'Admin' && !empty($hotel_id)) {
        $hotel_id = null; // Admins should not be assigned to specific hotels
    }
    
    try {
        // Check if email already exists
        $check_query = "SELECT EmployeeID FROM Employees WHERE Email = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Email already exists']);
            exit();
        }
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new employee
        $insert_query = "INSERT INTO Employees (FirstName, LastName, Email, Password, Role, HotelID, CreatedAt) VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("sssssi", $first_name, $last_name, $email, $hashed_password, $role, $hotel_id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Employee added successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Error adding employee: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
