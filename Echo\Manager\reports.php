<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    header('Location: login.php');
    exit();
}

// Get date parameters (default to today and next 7 days)
$start_date = $_GET['start_date'] ?? date('Y-m-d');
$end_date = $_GET['end_date'] ?? date('Y-m-d', strtotime('+7 days'));
$report_type = $_GET['report_type'] ?? 'current';

// Get occupancy report for selected date
$occupancy_report = get_occupancy_report($start_date);

// Get revenue summary for current month
$current_month = date('Y-m');
$revenue_query = "
    SELECT
        SUM(b.TotalAmount) as TotalRevenue,
        COUNT(DISTINCT r.ReservationID) as TotalReservations,
        AVG(b.TotalAmount) as AverageBookingValue
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    WHERE DATE_FORMAT(b.BillingDateTime, '%Y-%m') = ?
    AND b.PaymentStatus = 'Paid'
";
$stmt = $conn->prepare($revenue_query);
$stmt->bind_param("s", $current_month);
$stmt->execute();
$revenue_result = $stmt->get_result();
$revenue_data = $revenue_result->fetch_assoc();

// Get future occupancy projections (next 30 days)
function get_future_occupancy_projections($days = 30) {
    global $conn;

    $projections = [];
    for ($i = 0; $i < $days; $i++) {
        $date = date('Y-m-d', strtotime("+$i days"));

        $query = "
            SELECT
                h.Name as HotelName,
                h.HotelID,
                COUNT(DISTINCT r.RoomID) as TotalRooms,
                COUNT(DISTINCT CASE WHEN res.ReservationID IS NOT NULL THEN res.ReservationID END) as ProjectedOccupancy,
                SUM(CASE WHEN res.ReservationID IS NOT NULL THEN
                    DATEDIFF(LEAST(res.EndDate, ?), GREATEST(res.StartDate, ?)) * rt.DailyRate
                    ELSE 0 END) as ProjectedRevenue
            FROM Hotels h
            LEFT JOIN Rooms r ON h.HotelID = r.HotelID AND r.Status = 'Available'
            LEFT JOIN Reservations res ON r.HotelID = res.HotelID
                AND res.Status = 'Confirmed'
                AND ? BETWEEN res.StartDate AND DATE_SUB(res.EndDate, INTERVAL 1 DAY)
            LEFT JOIN RoomTypes rt ON res.RoomTypeID = rt.RoomTypeID
            GROUP BY h.HotelID, h.Name
            ORDER BY h.Name
        ";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("sss", $date, $date, $date);
        $stmt->execute();
        $result = $stmt->get_result();

        $projections[$date] = [];
        while ($row = $result->fetch_assoc()) {
            $occupancy_rate = $row['TotalRooms'] > 0 ?
                ($row['ProjectedOccupancy'] / $row['TotalRooms']) * 100 : 0;

            $projections[$date][] = [
                'hotel_name' => $row['HotelName'],
                'hotel_id' => $row['HotelID'],
                'total_rooms' => $row['TotalRooms'],
                'projected_occupancy' => $row['ProjectedOccupancy'],
                'occupancy_rate' => round($occupancy_rate, 1),
                'projected_revenue' => $row['ProjectedRevenue'] ?? 0
            ];
        }
    }

    return $projections;
}

$future_projections = get_future_occupancy_projections(14); // Next 14 days

// Get booking pipeline analysis
function get_booking_pipeline_analysis($days = 30) {
    global $conn;

    $pipeline_query = "
        SELECT
            h.Name as HotelName,
            h.HotelID,
            COUNT(CASE WHEN r.Status = 'Confirmed' AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY) THEN 1 END) as ConfirmedBookings,
            COUNT(CASE WHEN r.Status = 'Pending' AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY) THEN 1 END) as PendingBookings,
            COUNT(CASE WHEN r.Status = 'Cancelled' AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY) THEN 1 END) as CancelledBookings,
            SUM(CASE WHEN r.Status = 'Confirmed' AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as ConfirmedRevenue,
            SUM(CASE WHEN r.Status = 'Pending' AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as PendingRevenue,
            AVG(CASE WHEN r.Status = 'Confirmed' AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                THEN DATEDIFF(r.EndDate, r.StartDate) ELSE NULL END) as AvgStayDuration,
            COUNT(CASE WHEN r.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY) THEN 1 END) as RecentBookings
        FROM Hotels h
        LEFT JOIN Reservations r ON h.HotelID = r.HotelID
        LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        GROUP BY h.HotelID, h.Name
        ORDER BY h.Name
    ";

    $stmt = $conn->prepare($pipeline_query);
    $stmt->bind_param("iiiiiii", $days, $days, $days, $days, $days, $days, $days);
    $stmt->execute();
    return $stmt->get_result();
}

// Get booking trends by week
function get_booking_trends($weeks = 8) {
    global $conn;

    $trends_query = "
        SELECT
            YEARWEEK(r.StartDate) as WeekYear,
            WEEK(r.StartDate) as WeekNumber,
            YEAR(r.StartDate) as Year,
            COUNT(r.ReservationID) as TotalBookings,
            COUNT(CASE WHEN r.Status = 'Confirmed' THEN 1 END) as ConfirmedBookings,
            SUM(CASE WHEN r.Status = 'Confirmed' THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as WeeklyRevenue,
            AVG(DATEDIFF(r.EndDate, r.StartDate)) as AvgStayLength
        FROM Reservations r
        LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        WHERE r.StartDate BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? WEEK)
        GROUP BY YEARWEEK(r.StartDate), WEEK(r.StartDate), YEAR(r.StartDate)
        ORDER BY WeekYear ASC
    ";

    $stmt = $conn->prepare($trends_query);
    $stmt->bind_param("i", $weeks);
    $stmt->execute();
    return $stmt->get_result();
}

$booking_pipeline = get_booking_pipeline_analysis(30);
$booking_trends = get_booking_trends(8);

// Get comparative analysis data
function get_comparative_analysis($current_start, $current_end) {
    global $conn;

    // Calculate previous period dates
    $current_days = (strtotime($current_end) - strtotime($current_start)) / (60 * 60 * 24);
    $previous_start = date('Y-m-d', strtotime($current_start . " -$current_days days"));
    $previous_end = date('Y-m-d', strtotime($current_end . " -$current_days days"));

    $comparison_query = "
        SELECT
            h.Name as HotelName,
            h.HotelID,
            -- Current Period
            COUNT(CASE WHEN r.StartDate BETWEEN ? AND ? THEN 1 END) as CurrentBookings,
            SUM(CASE WHEN r.StartDate BETWEEN ? AND ? AND r.Status = 'Confirmed'
                THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as CurrentRevenue,
            AVG(CASE WHEN r.StartDate BETWEEN ? AND ?
                THEN DATEDIFF(r.EndDate, r.StartDate) ELSE NULL END) as CurrentAvgStay,
            -- Previous Period
            COUNT(CASE WHEN r.StartDate BETWEEN ? AND ? THEN 1 END) as PreviousBookings,
            SUM(CASE WHEN r.StartDate BETWEEN ? AND ? AND r.Status = 'Confirmed'
                THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as PreviousRevenue,
            AVG(CASE WHEN r.StartDate BETWEEN ? AND ?
                THEN DATEDIFF(r.EndDate, r.StartDate) ELSE NULL END) as PreviousAvgStay
        FROM Hotels h
        LEFT JOIN Reservations r ON h.HotelID = r.HotelID
        LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        GROUP BY h.HotelID, h.Name
        ORDER BY h.Name
    ";

    $stmt = $conn->prepare($comparison_query);
    $stmt->bind_param("ssssssssssss",
        $current_start, $current_end, $current_start, $current_end, $current_start, $current_end,
        $previous_start, $previous_end, $previous_start, $previous_end, $previous_start, $previous_end
    );
    $stmt->execute();
    return $stmt->get_result();
}

// Get year-over-year comparison
function get_year_over_year_comparison($month = null) {
    global $conn;

    if (!$month) {
        $month = date('m');
    }
    $current_year = date('Y');
    $previous_year = $current_year - 1;

    $yoy_query = "
        SELECT
            h.Name as HotelName,
            h.HotelID,
            -- Current Year
            COUNT(CASE WHEN YEAR(r.StartDate) = ? AND MONTH(r.StartDate) = ? THEN 1 END) as CurrentYearBookings,
            SUM(CASE WHEN YEAR(r.StartDate) = ? AND MONTH(r.StartDate) = ? AND r.Status = 'Confirmed'
                THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as CurrentYearRevenue,
            -- Previous Year
            COUNT(CASE WHEN YEAR(r.StartDate) = ? AND MONTH(r.StartDate) = ? THEN 1 END) as PreviousYearBookings,
            SUM(CASE WHEN YEAR(r.StartDate) = ? AND MONTH(r.StartDate) = ? AND r.Status = 'Confirmed'
                THEN DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate ELSE 0 END) as PreviousYearRevenue
        FROM Hotels h
        LEFT JOIN Reservations r ON h.HotelID = r.HotelID
        LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        GROUP BY h.HotelID, h.Name
        ORDER BY h.Name
    ";

    $stmt = $conn->prepare($yoy_query);
    $stmt->bind_param("iiiiiiii",
        $current_year, $month, $current_year, $month,
        $previous_year, $month, $previous_year, $month
    );
    $stmt->execute();
    return $stmt->get_result();
}

$comparative_analysis = null;
$year_over_year = null;

if ($report_type === 'comparative') {
    $comparative_analysis = get_comparative_analysis($start_date, $end_date);
    $year_over_year = get_year_over_year_comparison();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Reports & Analytics</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['employee_name']); ?>
                </div>
            </div>

            <div class="content">
                <!-- Enhanced Date Range Filter -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fa fa-filter"></i> Report Filters</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="report_type" class="form-label">Report Type</label>
                                <select class="form-control" id="report_type" name="report_type">
                                    <option value="current" <?php echo $report_type === 'current' ? 'selected' : ''; ?>>Current Occupancy</option>
                                    <option value="future" <?php echo $report_type === 'future' ? 'selected' : ''; ?>>Future Projections</option>
                                    <option value="historical" <?php echo $report_type === 'historical' ? 'selected' : ''; ?>>Historical Analysis</option>
                                    <option value="comparative" <?php echo $report_type === 'comparative' ? 'selected' : ''; ?>>Comparative Analysis</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-search"></i> Generate Report
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                        <i class="fa fa-refresh"></i> Reset
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Quick Action Buttons -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="setQuickReport('today')">
                                        <i class="fa fa-calendar-o"></i> Today
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="setQuickReport('week')">
                                        <i class="fa fa-calendar"></i> Next 7 Days
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="setQuickReport('month')">
                                        <i class="fa fa-calendar-plus-o"></i> Next 30 Days
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="printReport()">
                                        <i class="fa fa-print"></i> Print
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToCSV()">
                                        <i class="fa fa-download"></i> Export CSV
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Revenue Summary -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5>Monthly Revenue</h5>
                                <h3><?php echo format_currency($revenue_data['TotalRevenue'] ?? 0); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5>Total Reservations</h5>
                                <h3><?php echo number_format($revenue_data['TotalReservations'] ?? 0); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5>Average Booking Value</h5>
                                <h3><?php echo format_currency($revenue_data['AverageBookingValue'] ?? 0); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Occupancy Report -->
                <?php if ($report_type === 'current' || $report_type === 'historical'): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fa fa-calendar"></i> Daily Occupancy Report - <?php echo $start_date; ?></h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Hotel</th>
                                        <th>Total Rooms</th>
                                        <th>Occupied Rooms</th>
                                        <th>Occupancy Rate</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($hotel_data = $occupancy_report->fetch_assoc()): ?>
                                    <?php
                                        $occupancy_rate = $hotel_data['TotalRooms'] > 0 ?
                                            ($hotel_data['OccupiedRooms'] / $hotel_data['TotalRooms']) * 100 : 0;
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($hotel_data['HotelName']); ?></td>
                                        <td><?php echo htmlspecialchars($hotel_data['TotalRooms']); ?></td>
                                        <td><?php echo htmlspecialchars($hotel_data['OccupiedRooms']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $occupancy_rate >= 80 ? 'bg-success' : ($occupancy_rate >= 60 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($occupancy_rate, 1); ?>%
                                            </span>
                                        </td>
                                        <td><?php echo format_currency($hotel_data['Revenue']); ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Future Occupancy Projections -->
                <?php if ($report_type === 'future' || $report_type === 'comparative'): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fa fa-line-chart"></i> Future Occupancy Projections (Next 14 Days)</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Day</th>
                                        <?php
                                        // Get unique hotel names for column headers
                                        $hotel_names = [];
                                        foreach ($future_projections as $date => $hotels) {
                                            foreach ($hotels as $hotel) {
                                                if (!in_array($hotel['hotel_name'], $hotel_names)) {
                                                    $hotel_names[] = $hotel['hotel_name'];
                                                }
                                            }
                                            break; // Only need first date to get hotel names
                                        }
                                        foreach ($hotel_names as $hotel_name): ?>
                                            <th><?php echo htmlspecialchars($hotel_name); ?></th>
                                        <?php endforeach; ?>
                                        <th>Total Projected Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($future_projections as $date => $hotels): ?>
                                    <tr>
                                        <td><strong><?php echo date('M d, Y', strtotime($date)); ?></strong></td>
                                        <td><?php echo date('l', strtotime($date)); ?></td>
                                        <?php
                                        $total_daily_revenue = 0;
                                        foreach ($hotel_names as $hotel_name):
                                            $hotel_data = null;
                                            foreach ($hotels as $hotel) {
                                                if ($hotel['hotel_name'] === $hotel_name) {
                                                    $hotel_data = $hotel;
                                                    break;
                                                }
                                            }
                                            if ($hotel_data):
                                                $total_daily_revenue += $hotel_data['projected_revenue'];
                                        ?>
                                            <td>
                                                <div class="d-flex justify-content-between">
                                                    <span><?php echo $hotel_data['projected_occupancy']; ?>/<?php echo $hotel_data['total_rooms']; ?></span>
                                                    <span class="badge <?php echo $hotel_data['occupancy_rate'] >= 80 ? 'bg-success' : ($hotel_data['occupancy_rate'] >= 60 ? 'bg-warning' : 'bg-info'); ?>">
                                                        <?php echo $hotel_data['occupancy_rate']; ?>%
                                                    </span>
                                                </div>
                                                <small class="text-muted"><?php echo format_currency($hotel_data['projected_revenue']); ?></small>
                                            </td>
                                        <?php else: ?>
                                            <td><span class="text-muted">No data</span></td>
                                        <?php endif; endforeach; ?>
                                        <td><strong><?php echo format_currency($total_daily_revenue); ?></strong></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Booking Pipeline Analysis -->
                <?php if ($report_type === 'future' || $report_type === 'comparative'): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fa fa-tasks"></i> Booking Pipeline Analysis (Next 30 Days)</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Hotel</th>
                                        <th>Confirmed Bookings</th>
                                        <th>Pending Bookings</th>
                                        <th>Cancelled Bookings</th>
                                        <th>Confirmed Revenue</th>
                                        <th>Pending Revenue</th>
                                        <th>Avg Stay Duration</th>
                                        <th>Recent Bookings (7 days)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($pipeline_data = $booking_pipeline->fetch_assoc()): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($pipeline_data['HotelName']); ?></strong></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo $pipeline_data['ConfirmedBookings']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">
                                                <?php echo $pipeline_data['PendingBookings']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">
                                                <?php echo $pipeline_data['CancelledBookings']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong class="text-success">
                                                <?php echo format_currency($pipeline_data['ConfirmedRevenue']); ?>
                                            </strong>
                                        </td>
                                        <td>
                                            <span class="text-warning">
                                                <?php echo format_currency($pipeline_data['PendingRevenue']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo number_format($pipeline_data['AvgStayDuration'] ?? 0, 1); ?> days
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $pipeline_data['RecentBookings']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Booking Trends Analysis -->
                <?php if ($report_type === 'future' || $report_type === 'comparative'): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fa fa-line-chart"></i> Booking Trends (Next 8 Weeks)</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Week</th>
                                        <th>Year</th>
                                        <th>Total Bookings</th>
                                        <th>Confirmed Bookings</th>
                                        <th>Confirmation Rate</th>
                                        <th>Weekly Revenue</th>
                                        <th>Avg Stay Length</th>
                                        <th>Trend</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $previous_bookings = 0;
                                    while ($trend_data = $booking_trends->fetch_assoc()):
                                        $confirmation_rate = $trend_data['TotalBookings'] > 0 ?
                                            ($trend_data['ConfirmedBookings'] / $trend_data['TotalBookings']) * 100 : 0;

                                        $trend_direction = '';
                                        if ($previous_bookings > 0) {
                                            if ($trend_data['TotalBookings'] > $previous_bookings) {
                                                $trend_direction = '<i class="fa fa-arrow-up text-success"></i> Up';
                                            } elseif ($trend_data['TotalBookings'] < $previous_bookings) {
                                                $trend_direction = '<i class="fa fa-arrow-down text-danger"></i> Down';
                                            } else {
                                                $trend_direction = '<i class="fa fa-minus text-muted"></i> Stable';
                                            }
                                        }
                                        $previous_bookings = $trend_data['TotalBookings'];
                                    ?>
                                    <tr>
                                        <td><strong>Week <?php echo $trend_data['WeekNumber']; ?></strong></td>
                                        <td><?php echo $trend_data['Year']; ?></td>
                                        <td><?php echo $trend_data['TotalBookings']; ?></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo $trend_data['ConfirmedBookings']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $confirmation_rate >= 80 ? 'bg-success' : ($confirmation_rate >= 60 ? 'bg-warning' : 'bg-danger'); ?>">
                                                <?php echo number_format($confirmation_rate, 1); ?>%
                                            </span>
                                        </td>
                                        <td><?php echo format_currency($trend_data['WeeklyRevenue']); ?></td>
                                        <td><?php echo number_format($trend_data['AvgStayLength'] ?? 0, 1); ?> days</td>
                                        <td><?php echo $trend_direction; ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Comparative Analysis -->
                <?php if ($report_type === 'comparative' && $comparative_analysis): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fa fa-bar-chart"></i> Period-over-Period Comparison</h3>
                        <small class="text-muted">
                            Comparing <?php echo $start_date; ?> to <?php echo $end_date; ?>
                            vs Previous Period
                        </small>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Hotel</th>
                                        <th>Current Bookings</th>
                                        <th>Previous Bookings</th>
                                        <th>Booking Change</th>
                                        <th>Current Revenue</th>
                                        <th>Previous Revenue</th>
                                        <th>Revenue Change</th>
                                        <th>Avg Stay Change</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($comp_data = $comparative_analysis->fetch_assoc()):
                                        $booking_change = $comp_data['PreviousBookings'] > 0 ?
                                            (($comp_data['CurrentBookings'] - $comp_data['PreviousBookings']) / $comp_data['PreviousBookings']) * 100 : 0;
                                        $revenue_change = $comp_data['PreviousRevenue'] > 0 ?
                                            (($comp_data['CurrentRevenue'] - $comp_data['PreviousRevenue']) / $comp_data['PreviousRevenue']) * 100 : 0;
                                        $stay_change = ($comp_data['PreviousAvgStay'] > 0 && $comp_data['CurrentAvgStay'] > 0) ?
                                            (($comp_data['CurrentAvgStay'] - $comp_data['PreviousAvgStay']) / $comp_data['PreviousAvgStay']) * 100 : 0;
                                    ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($comp_data['HotelName']); ?></strong></td>
                                        <td><?php echo $comp_data['CurrentBookings']; ?></td>
                                        <td><?php echo $comp_data['PreviousBookings']; ?></td>
                                        <td>
                                            <span class="badge <?php echo $booking_change >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo ($booking_change >= 0 ? '+' : '') . number_format($booking_change, 1); ?>%
                                            </span>
                                        </td>
                                        <td><?php echo format_currency($comp_data['CurrentRevenue']); ?></td>
                                        <td><?php echo format_currency($comp_data['PreviousRevenue']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $revenue_change >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo ($revenue_change >= 0 ? '+' : '') . number_format($revenue_change, 1); ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $stay_change >= 0 ? 'bg-info' : 'bg-warning'; ?>">
                                                <?php echo ($stay_change >= 0 ? '+' : '') . number_format($stay_change, 1); ?>%
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Year-over-Year Comparison -->
                <?php if ($report_type === 'comparative' && $year_over_year): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h3><i class="fa fa-calendar"></i> Year-over-Year Comparison</h3>
                        <small class="text-muted">
                            Comparing <?php echo date('F Y'); ?> vs <?php echo date('F Y', strtotime('-1 year')); ?>
                        </small>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Hotel</th>
                                        <th><?php echo date('Y'); ?> Bookings</th>
                                        <th><?php echo date('Y', strtotime('-1 year')); ?> Bookings</th>
                                        <th>Booking Growth</th>
                                        <th><?php echo date('Y'); ?> Revenue</th>
                                        <th><?php echo date('Y', strtotime('-1 year')); ?> Revenue</th>
                                        <th>Revenue Growth</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($yoy_data = $year_over_year->fetch_assoc()):
                                        $booking_growth = $yoy_data['PreviousYearBookings'] > 0 ?
                                            (($yoy_data['CurrentYearBookings'] - $yoy_data['PreviousYearBookings']) / $yoy_data['PreviousYearBookings']) * 100 : 0;
                                        $revenue_growth = $yoy_data['PreviousYearRevenue'] > 0 ?
                                            (($yoy_data['CurrentYearRevenue'] - $yoy_data['PreviousYearRevenue']) / $yoy_data['PreviousYearRevenue']) * 100 : 0;

                                        $performance = 'Stable';
                                        $performance_class = 'bg-secondary';
                                        if ($booking_growth > 10 && $revenue_growth > 10) {
                                            $performance = 'Excellent';
                                            $performance_class = 'bg-success';
                                        } elseif ($booking_growth > 0 && $revenue_growth > 0) {
                                            $performance = 'Good';
                                            $performance_class = 'bg-info';
                                        } elseif ($booking_growth < -10 || $revenue_growth < -10) {
                                            $performance = 'Needs Attention';
                                            $performance_class = 'bg-danger';
                                        } elseif ($booking_growth < 0 || $revenue_growth < 0) {
                                            $performance = 'Declining';
                                            $performance_class = 'bg-warning';
                                        }
                                    ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($yoy_data['HotelName']); ?></strong></td>
                                        <td><?php echo $yoy_data['CurrentYearBookings']; ?></td>
                                        <td><?php echo $yoy_data['PreviousYearBookings']; ?></td>
                                        <td>
                                            <span class="badge <?php echo $booking_growth >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo ($booking_growth >= 0 ? '+' : '') . number_format($booking_growth, 1); ?>%
                                            </span>
                                        </td>
                                        <td><?php echo format_currency($yoy_data['CurrentYearRevenue']); ?></td>
                                        <td><?php echo format_currency($yoy_data['PreviousYearRevenue']); ?></td>
                                        <td>
                                            <span class="badge <?php echo $revenue_growth >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo ($revenue_growth >= 0 ? '+' : '') . number_format($revenue_growth, 1); ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $performance_class; ?>">
                                                <?php echo $performance; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        // Reset filters to default values
        function resetFilters() {
            document.getElementById('start_date').value = '<?php echo date('Y-m-d'); ?>';
            document.getElementById('end_date').value = '<?php echo date('Y-m-d', strtotime('+7 days')); ?>';
            document.getElementById('report_type').value = 'current';
        }

        // Auto-submit form when report type changes
        document.getElementById('report_type').addEventListener('change', function() {
            this.form.submit();
        });

        // Add print functionality
        function printReport() {
            window.print();
        }

        // Add export functionality (basic CSV export)
        function exportToCSV() {
            const table = document.querySelector('.table');
            if (!table) return;

            let csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length; j++) {
                    row.push(cols[j].innerText);
                }

                csv.push(row.join(','));
            }

            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = 'hotel_report_' + new Date().toISOString().split('T')[0] + '.csv';
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }

        // Add quick date range buttons
        function setDateRange(days) {
            const today = new Date();
            const endDate = new Date(today);
            endDate.setDate(today.getDate() + days);

            document.getElementById('start_date').value = today.toISOString().split('T')[0];
            document.getElementById('end_date').value = endDate.toISOString().split('T')[0];
        }

        // Quick report functions
        function setQuickReport(type) {
            const today = new Date();
            let endDate = new Date(today);
            let reportType = 'future';

            switch(type) {
                case 'today':
                    reportType = 'current';
                    break;
                case 'week':
                    endDate.setDate(today.getDate() + 7);
                    break;
                case 'month':
                    endDate.setDate(today.getDate() + 30);
                    break;
            }

            document.getElementById('start_date').value = today.toISOString().split('T')[0];
            document.getElementById('end_date').value = endDate.toISOString().split('T')[0];
            document.getElementById('report_type').value = reportType;

            // Submit the form
            document.querySelector('form').submit();
        }

        // Initialize tooltips if using Bootstrap
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>

    <style>
        .badge {
            font-size: 0.8rem;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0,0,0,.075);
        }

        .card-header h3, .card-header h5 {
            margin: 0;
            color: #333;
        }

        .btn-group .btn {
            margin-right: 5px;
        }

        @media print {
            .card-header, .btn, .form-control {
                -webkit-print-color-adjust: exact;
            }
        }

        .text-muted {
            font-size: 0.85rem;
        }

        .d-flex {
            display: flex !important;
        }

        .justify-content-between {
            justify-content: space-between !important;
        }
    </style>

    <script>
        // Reset filters function
        function resetFilters() {
            document.getElementById('start_date').value = '<?php echo date('Y-m-d'); ?>';
            document.getElementById('end_date').value = '<?php echo date('Y-m-d', strtotime('+7 days')); ?>';
            document.getElementById('report_type').value = 'current';
        }

        // Set quick report dates
        function setQuickReport(period) {
            const startDate = document.getElementById('start_date');
            const endDate = document.getElementById('end_date');
            const today = new Date();

            switch(period) {
                case 'today':
                    startDate.value = today.toISOString().split('T')[0];
                    endDate.value = today.toISOString().split('T')[0];
                    break;
                case 'week':
                    startDate.value = today.toISOString().split('T')[0];
                    const weekEnd = new Date(today);
                    weekEnd.setDate(today.getDate() + 7);
                    endDate.value = weekEnd.toISOString().split('T')[0];
                    break;
                case 'month':
                    startDate.value = today.toISOString().split('T')[0];
                    const monthEnd = new Date(today);
                    monthEnd.setDate(today.getDate() + 30);
                    endDate.value = monthEnd.toISOString().split('T')[0];
                    break;
            }

            // Auto-submit the form
            document.querySelector('form').submit();
        }

        // Print report function
        function printReport() {
            window.print();
        }

        // Export to CSV function
        function exportToCSV() {
            // Get all tables
            const tables = document.querySelectorAll('.table');
            let csvContent = '';

            tables.forEach((table, index) => {
                if (index > 0) csvContent += '\n\n';

                // Get table title
                const cardHeader = table.closest('.card').querySelector('.card-header h3');
                if (cardHeader) {
                    csvContent += cardHeader.textContent.trim() + '\n';
                }

                // Get table headers
                const headers = table.querySelectorAll('thead th');
                const headerRow = Array.from(headers).map(th => th.textContent.trim()).join(',');
                csvContent += headerRow + '\n';

                // Get table rows
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    const rowData = Array.from(cells).map(td => {
                        // Clean up cell content (remove HTML tags and extra spaces)
                        return td.textContent.trim().replace(/,/g, ';');
                    }).join(',');
                    csvContent += rowData + '\n';
                });
            });

            // Create and download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'hotel_report_' + new Date().toISOString().split('T')[0] + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
