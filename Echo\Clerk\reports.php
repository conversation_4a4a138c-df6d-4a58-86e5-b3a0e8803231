<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];

// Get date range from form or default to current month
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');

// Get hotel name
$hotel_query = "SELECT Name FROM Hotels WHERE HotelID = ?";
$stmt = $conn->prepare($hotel_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$hotel_name = $stmt->get_result()->fetch_assoc()['Name'];

// Occupancy Report
$occupancy_query = "
    SELECT
        DATE(ci.CheckInDateTime) as Date,
        COUNT(DISTINCT ci.RoomID) as OccupiedRooms,
        (SELECT COUNT(*) FROM Rooms WHERE HotelID = ? AND Status != 'Maintenance') as TotalRooms,
        SUM(b.TotalAmount) as DailyRevenue
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Billing b ON ci.CheckInID = b.CheckInID
    WHERE r.HotelID = ?
    AND DATE(ci.CheckInDateTime) BETWEEN ? AND ?
    AND (ci.ActualCheckOutDateTime IS NULL OR DATE(ci.ActualCheckOutDateTime) > DATE(ci.CheckInDateTime))
    GROUP BY DATE(ci.CheckInDateTime)
    ORDER BY Date DESC
";
$stmt = $conn->prepare($occupancy_query);
$stmt->bind_param("iiss", $clerk_hotel_id, $clerk_hotel_id, $start_date, $end_date);
$stmt->execute();
$occupancy_result = $stmt->get_result();

// Revenue Summary
$revenue_query = "
    SELECT
        SUM(b.TotalAmount) as TotalRevenue,
        COUNT(DISTINCT b.BillingID) as TotalBills,
        AVG(b.TotalAmount) as AverageRevenue,
        SUM(CASE WHEN b.PaymentStatus = 'Paid' THEN b.TotalAmount ELSE 0 END) as PaidRevenue,
        SUM(CASE WHEN b.PaymentStatus = 'Pending' THEN b.TotalAmount ELSE 0 END) as PendingRevenue
    FROM Billing b
    JOIN Reservations r ON b.ReservationID = r.ReservationID
    WHERE r.HotelID = ? AND DATE(b.BillingDateTime) BETWEEN ? AND ?
";
$stmt = $conn->prepare($revenue_query);
$stmt->bind_param("iss", $clerk_hotel_id, $start_date, $end_date);
$stmt->execute();
$revenue_summary = $stmt->get_result()->fetch_assoc();

// Room Type Performance
$room_type_query = "
    SELECT
        rt.TypeName,
        COUNT(DISTINCT r.ReservationID) as Bookings,
        SUM(DATEDIFF(r.EndDate, r.StartDate)) as TotalNights,
        SUM(b.TotalAmount) as Revenue,
        AVG(b.TotalAmount) as AvgRevenue
    FROM Reservations r
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    LEFT JOIN Billing b ON r.ReservationID = b.ReservationID
    WHERE r.HotelID = ? AND r.StartDate BETWEEN ? AND ?
    GROUP BY rt.RoomTypeID, rt.TypeName
    ORDER BY Revenue DESC
";
$stmt = $conn->prepare($room_type_query);
$stmt->bind_param("iss", $clerk_hotel_id, $start_date, $end_date);
$stmt->execute();
$room_type_result = $stmt->get_result();

// Guest Statistics
$guest_stats_query = "
    SELECT
        COUNT(DISTINCT r.CustomerID) as UniqueGuests,
        COUNT(r.ReservationID) as TotalReservations,
        SUM(r.NumberOfGuests) as TotalGuestNights,
        AVG(r.NumberOfGuests) as AvgGuestsPerReservation
    FROM Reservations r
    WHERE r.HotelID = ? AND r.StartDate BETWEEN ? AND ?
";
$stmt = $conn->prepare($guest_stats_query);
$stmt->bind_param("iss", $clerk_hotel_id, $start_date, $end_date);
$stmt->execute();
$guest_stats = $stmt->get_result()->fetch_assoc();

// Recent Activities
$activities_query = "
    SELECT
        'Check-in' as Activity,
        CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as GuestName,
        rm.RoomNumber,
        ci.CheckInDateTime as ActivityTime
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN Rooms rm ON ci.RoomID = rm.RoomID
    WHERE r.HotelID = ? AND DATE(ci.CheckInDateTime) BETWEEN ? AND ?

    UNION ALL

    SELECT
        'Check-out' as Activity,
        CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as GuestName,
        rm.RoomNumber,
        ci.ActualCheckOutDateTime as ActivityTime
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN Rooms rm ON ci.RoomID = rm.RoomID
    WHERE r.HotelID = ? AND ci.ActualCheckOutDateTime IS NOT NULL
    AND DATE(ci.ActualCheckOutDateTime) BETWEEN ? AND ?

    ORDER BY ActivityTime DESC
    LIMIT 20
";
$stmt = $conn->prepare($activities_query);
$stmt->bind_param("isssss", $clerk_hotel_id, $start_date, $end_date, $clerk_hotel_id, $start_date, $end_date);
$stmt->execute();
$activities_result = $stmt->get_result();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Reports - <?php echo htmlspecialchars($hotel_name); ?></h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <!-- Date Range Filter -->
                <div class="card mb-3">
                    <div class="card-body">
                        <form method="GET" class="row">
                            <div class="col-md-4">
                                <label>Start Date</label>
                                <input type="date" name="start_date" class="form-control" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <label>End Date</label>
                                <input type="date" name="end_date" class="form-control" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">Generate Report</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Revenue Summary -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card primary">
                            <h3><?php echo format_currency($revenue_summary['TotalRevenue'] ?? 0); ?></h3>
                            <p>Total Revenue</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card success">
                            <h3><?php echo format_currency($revenue_summary['PaidRevenue'] ?? 0); ?></h3>
                            <p>Paid Revenue</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card warning">
                            <h3><?php echo $revenue_summary['TotalBills'] ?? 0; ?></h3>
                            <p>Total Bills</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card info">
                            <h3><?php echo format_currency($revenue_summary['AverageRevenue'] ?? 0); ?></h3>
                            <p>Average Bill</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Occupancy Report -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Daily Occupancy</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Occupied</th>
                                                <th>Total</th>
                                                <th>Rate</th>
                                                <th>Revenue</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($occupancy = $occupancy_result->fetch_assoc()): ?>
                                            <?php $occupancy_rate = ($occupancy['OccupiedRooms'] / $occupancy['TotalRooms']) * 100; ?>
                                            <tr>
                                                <td><?php echo date('M d', strtotime($occupancy['Date'])); ?></td>
                                                <td><?php echo $occupancy['OccupiedRooms']; ?></td>
                                                <td><?php echo $occupancy['TotalRooms']; ?></td>
                                                <td><?php echo number_format($occupancy_rate, 1); ?>%</td>
                                                <td><?php echo format_currency($occupancy['DailyRevenue'] ?? 0); ?></td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Room Type Performance -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Room Type Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Room Type</th>
                                                <th>Bookings</th>
                                                <th>Nights</th>
                                                <th>Revenue</th>
                                                <th>Avg/Booking</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($room_type = $room_type_result->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($room_type['TypeName']); ?></td>
                                                <td><?php echo $room_type['Bookings']; ?></td>
                                                <td><?php echo $room_type['TotalNights']; ?></td>
                                                <td><?php echo format_currency($room_type['Revenue'] ?? 0); ?></td>
                                                <td><?php echo format_currency($room_type['AvgRevenue'] ?? 0); ?></td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Guest Statistics -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Guest Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h4><?php echo $guest_stats['UniqueGuests']; ?></h4>
                                        <p class="text-muted">Unique Guests</p>
                                    </div>
                                    <div class="col-6">
                                        <h4><?php echo $guest_stats['TotalReservations']; ?></h4>
                                        <p class="text-muted">Total Reservations</p>
                                    </div>
                                    <div class="col-6">
                                        <h4><?php echo $guest_stats['TotalGuestNights']; ?></h4>
                                        <p class="text-muted">Total Guest Nights</p>
                                    </div>
                                    <div class="col-6">
                                        <h4><?php echo number_format($guest_stats['AvgGuestsPerReservation'], 1); ?></h4>
                                        <p class="text-muted">Avg Guests/Booking</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Recent Activities</h5>
                            </div>
                            <div class="card-body">
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <?php while ($activity = $activities_result->fetch_assoc()): ?>
                                    <div class="mb-2 p-2 border-left border-<?php echo $activity['Activity'] === 'Check-in' ? 'success' : 'info'; ?>">
                                        <strong><?php echo $activity['Activity']; ?></strong> -
                                        <?php echo htmlspecialchars($activity['GuestName']); ?>
                                        <br><small class="text-muted">
                                            Room <?php echo $activity['RoomNumber']; ?> -
                                            <?php echo date('M d, H:i', strtotime($activity['ActivityTime'])); ?>
                                        </small>
                                    </div>
                                    <?php endwhile; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Print Button -->
                <div class="text-center mt-4">
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fa fa-print"></i> Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
</body>
</html>
