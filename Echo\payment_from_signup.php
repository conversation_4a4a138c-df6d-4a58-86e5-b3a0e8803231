<?php
session_start();
require_once('config/db.php');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit();
}

// Check if booking data exists in session
if (!isset($_SESSION['booking_data'])) {
    header('Location: index.php');
    exit();
}

$booking_data = $_SESSION['booking_data'];

// Extract booking data
$hotel_id = $booking_data['hotel_id'];
$room_type_id = $booking_data['room_type_id'];
$check_in = $booking_data['check_in'];
$check_out = $booking_data['check_out'];
$guests = $booking_data['guests'];
$booking_type = $booking_data['booking_type'];
$rate_type = $booking_data['rate_type'];

// Travel agent specific fields
$company_name = $booking_data['company_name'];
$rooms_count = (int)$booking_data['rooms_count'];
$nights = (int)$booking_data['nights'];
$discount_rate = (int)$booking_data['discount_rate'];
$billing_info = $booking_data['billing_info'];

// Get hotel and room type information
$hotel_query = "SELECT * FROM hotels WHERE HotelID = ?";
$stmt = $conn->prepare($hotel_query);
$stmt->bind_param("i", $hotel_id);
$stmt->execute();
$hotel = $stmt->get_result()->fetch_assoc();

$room_type_query = "SELECT * FROM roomtypes WHERE RoomTypeID = ?";
$stmt = $conn->prepare($room_type_query);
$stmt->bind_param("i", $room_type_id);
$stmt->execute();
$room_type = $stmt->get_result()->fetch_assoc();

// Calculate pricing
$check_in_date = new DateTime($check_in);
$check_out_date = new DateTime($check_out);
$calculated_nights = $check_in_date->diff($check_out_date)->days;

// For travel agents, use the nights from form if provided
$nights_to_use = ($booking_type === 'agent' && $nights > 0) ? $nights : $calculated_nights;
$rooms_multiplier = ($booking_type === 'agent') ? $rooms_count : 1;

// Calculate rate options for Residential Suite
$rate_options = [];
if ($room_type['TypeName'] === 'Residential Suite') {
    // Daily rate
    $daily_total = $room_type['DailyRate'] * $nights_to_use * $rooms_multiplier;
    $rate_options['daily'] = [
        'rate' => $room_type['DailyRate'],
        'total' => $daily_total,
        'label' => 'Daily Rate',
        'unit' => 'per night'
    ];

    // Weekly rate (if 7+ nights)
    if ($nights_to_use >= 7 && $room_type['WeeklyRate']) {
        $weeks = ceil($nights_to_use / 7);
        $weekly_total = $room_type['WeeklyRate'] * $weeks * $rooms_multiplier;
        $rate_options['weekly'] = [
            'rate' => $room_type['WeeklyRate'],
            'total' => $weekly_total,
            'label' => 'Weekly Rate',
            'unit' => 'per week'
        ];
    }

    // Monthly rate (if 30+ nights)
    if ($nights_to_use >= 30 && $room_type['MonthlyRate']) {
        $months = ceil($nights_to_use / 30);
        $monthly_total = $room_type['MonthlyRate'] * $months * $rooms_multiplier;
        $rate_options['monthly'] = [
            'rate' => $room_type['MonthlyRate'],
            'total' => $monthly_total,
            'label' => 'Monthly Rate',
            'unit' => 'per month'
        ];
    }
} else {
    // For other room types, only daily rate
    $daily_total = $room_type['DailyRate'] * $nights_to_use * $rooms_multiplier;
    $rate_options['daily'] = [
        'rate' => $room_type['DailyRate'],
        'total' => $daily_total,
        'label' => 'Daily Rate',
        'unit' => 'per night'
    ];
}

// Apply travel agent discount
if ($booking_type === 'agent' && $discount_rate > 0) {
    foreach ($rate_options as $key => $rate_data) {
        $discount_amount = $rate_data['total'] * ($discount_rate / 100);
        $rate_options[$key]['discount_amount'] = $discount_amount;
        $rate_options[$key]['discounted_total'] = $rate_data['total'] - $discount_amount;
    }
}

$user_type = $_SESSION['user_type'];

// Clear booking data from session as we'll use form data from here
unset($_SESSION['booking_data']);
?>
<!DOCTYPE html>
<html lang="zxx">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Your Reservation - Echo Hotels</title>

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css?family=Lora:400,700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Cabin:400,500,600,700&display=swap" rel="stylesheet">

    <!-- Css Styles -->
    <link rel="stylesheet" href="css/bootstrap.min.css" type="text/css">
    <link rel="stylesheet" href="css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="css/elegant-icons.css" type="text/css">
    <link rel="stylesheet" href="css/flaticon.css" type="text/css">
    <link rel="stylesheet" href="css/owl.carousel.min.css" type="text/css">
    <link rel="stylesheet" href="css/nice-select.css" type="text/css">
    <link rel="stylesheet" href="css/jquery-ui.min.css" type="text/css">
    <link rel="stylesheet" href="css/magnific-popup.css" type="text/css">
    <link rel="stylesheet" href="css/slicknav.min.css" type="text/css">
    <link rel="stylesheet" href="css/style.css" type="text/css">

    <style>
        .payment-section {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .payment-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .welcome-message {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .welcome-message h4 {
            margin: 0;
            font-size: 20px;
        }

        .booking-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #dfa974;
        }

        .booking-summary h5 {
            color: #dfa974;
            margin-bottom: 15px;
        }

        .rate-selection {
            margin: 30px 0;
        }

        .rate-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .rate-option:hover {
            border-color: #dfa974;
        }

        .rate-option.selected {
            border-color: #dfa974;
            background: #fff8f0;
        }

        .rate-option input[type="radio"] {
            margin-right: 10px;
        }

        .payment-options {
            margin-top: 30px;
        }

        .payment-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-option:hover {
            border-color: #dfa974;
        }

        .payment-option.selected {
            border-color: #dfa974;
            background: #fff8f0;
        }

        .credit-card-section {
            margin-top: 20px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            border: 2px solid #e9ecef;
            display: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .credit-card-section.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .credit-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .credit-card-header i {
            font-size: 24px;
            color: #dfa974;
            margin-right: 10px;
        }

        .credit-card-header h6 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .card-form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .card-form-group {
            flex: 1;
            position: relative;
        }

        .card-form-group.full-width {
            flex: 100%;
        }

        .card-form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 14px;
        }

        .card-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
            box-sizing: border-box;
        }

        .card-input:focus {
            outline: none;
            border-color: #dfa974;
            box-shadow: 0 0 0 3px rgba(223, 169, 116, 0.1);
        }

        .card-input::placeholder {
            color: #adb5bd;
        }

        .card-number-input {
            font-family: 'Courier New', monospace;
            letter-spacing: 2px;
        }

        .security-note {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 1px solid #c3e6c3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            display: flex;
            align-items: center;
        }

        .security-note i {
            font-size: 20px;
            color: #28a745;
            margin-right: 10px;
        }

        .security-note .security-text {
            color: #155724;
            font-size: 14px;
            margin: 0;
        }

        .card-icons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .card-icon {
            width: 40px;
            height: 25px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #6c757d;
        }

        .card-icon.visa {
            background: #1a1f71;
            color: white;
        }

        .card-icon.mastercard {
            background: #eb001b;
            color: white;
        }

        .card-icon.amex {
            background: #006fcf;
            color: white;
        }

        @media (max-width: 768px) {
            .card-form-row {
                flex-direction: column;
                gap: 15px;
            }

            .credit-card-section {
                padding: 20px;
            }
        }

        .agent-billing-note {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .btn-confirm {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-confirm:hover {
            background: #218838;
        }
    </style>
</head>

<body>
    <!-- Header Section Begin -->
    <header class="header-section">
        <div class="top-nav">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <ul class="tn-left">
                            <li><i class="fa fa-phone"></i> (12) 345 67890</li>
                            <li><i class="fa fa-envelope"></i> <EMAIL></li>
                        </ul>
                    </div>
                    <div class="col-lg-6">
                        <div class="tn-right">
                            <a href="index.php" class="bk-btn">Back to Home</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="menu-item">
            <div class="container">
                <div class="row">
                    <div class="col-lg-2">
                        <div class="logo">
                            <a href="index.php">
                                <img src="img/logo.png" alt="">
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-10">
                        <div class="nav-menu">
                            <nav class="mainmenu">
                                <ul>
                                    <li><a href="index.php">Home</a></li>
                                    <li><a href="profile/<?php echo $user_type; ?>.php">Profile</a></li>
                                    <li><a href="auth/logout.php">Logout</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header End -->

    <!-- Payment Section Begin -->
    <section class="payment-section spad">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>Complete Your Reservation</span>
                        <h2>Welcome! Let's Finish Your Booking</h2>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8 offset-lg-2">
                    <!-- Welcome Message -->
                    <div class="welcome-message">
                        <h4><i class="fa fa-check-circle"></i> Registration Successful!</h4>
                        <p>Welcome to Echo Hotels, <?php echo htmlspecialchars($_SESSION['user_name']); ?>! Now let's complete your reservation.</p>
                    </div>

                    <div class="payment-card">
                        <!-- Booking Summary -->
                        <div class="booking-summary">
                            <h5><i class="fa fa-file-text"></i> Booking Summary</h5>
                            <p><strong>Hotel:</strong> <?php echo htmlspecialchars($hotel['Name']); ?> - <?php echo htmlspecialchars($hotel['Location']); ?></p>
                            <p><strong>Room Type:</strong> <?php echo htmlspecialchars($room_type['TypeName']); ?></p>
                            <p><strong>Booking Type:</strong> <?php echo $booking_type === 'agent' ? 'Travel Agent' : 'Guest'; ?></p>
                            <?php if ($booking_type === 'agent'): ?>
                            <p><strong>Company:</strong> <?php echo htmlspecialchars($company_name); ?></p>
                            <p><strong>Rooms:</strong> <?php echo $rooms_count; ?> room(s)</p>
                            <?php endif; ?>
                            <p><strong>Dates:</strong> <?php echo $check_in; ?> to <?php echo $check_out; ?></p>
                            <p><strong>Duration:</strong> <?php echo $nights_to_use; ?> night(s)</p>
                            <?php if ($booking_type === 'guest'): ?>
                            <p><strong>Guests:</strong> <?php echo $guests; ?></p>
                            <?php endif; ?>
                            <?php if ($booking_type === 'agent' && $discount_rate > 0): ?>
                            <p class="text-info"><strong><i class="fa fa-percent"></i> Block Booking Discount: <?php echo $discount_rate; ?>%</strong></p>
                            <?php endif; ?>
                        </div>

                        <!-- Rate Selection Form -->
                        <form id="paymentForm" method="POST" action="api/confirm_reservation.php">
                            <!-- Hidden fields to pass booking data -->
                            <input type="hidden" name="hotel_id" value="<?php echo $hotel_id; ?>">
                            <input type="hidden" name="room_type_id" value="<?php echo $room_type_id; ?>">
                            <input type="hidden" name="check_in" value="<?php echo $check_in; ?>">
                            <input type="hidden" name="check_out" value="<?php echo $check_out; ?>">
                            <input type="hidden" name="guests" value="<?php echo $guests; ?>">
                            <input type="hidden" name="booking_type" value="<?php echo $booking_type; ?>">

                            <!-- Travel agent specific fields -->
                            <?php if ($booking_type === 'agent'): ?>
                            <input type="hidden" name="company_name" value="<?php echo htmlspecialchars($company_name); ?>">
                            <input type="hidden" name="rooms_count" value="<?php echo $rooms_count; ?>">
                            <input type="hidden" name="nights" value="<?php echo $nights_to_use; ?>">
                            <input type="hidden" name="discount_rate" value="<?php echo $discount_rate; ?>">
                            <input type="hidden" name="billing_info" value="<?php echo htmlspecialchars($billing_info); ?>">
                            <?php endif; ?>

                            <!-- Rate Selection -->
                            <div class="rate-selection">
                                <h5><i class="fa fa-calculator"></i> Select Your Rate Option:</h5>

                                <div class="rate-options">
                                    <?php
                                    $first_option = true;
                                    foreach ($rate_options as $rate_type => $rate_data):
                                    ?>
                                    <div class="rate-option <?php echo $first_option ? 'selected' : ''; ?>">
                                        <input type="radio" name="rate_type" value="<?php echo $rate_type; ?>" <?php echo $first_option ? 'checked' : ''; ?>>
                                        <label>
                                            <strong><?php echo $rate_data['label']; ?></strong><br>
                                            Rate: LKR <?php echo number_format($rate_data['rate'], 2); ?> <?php echo $rate_data['unit']; ?><br>
                                            <?php if ($booking_type === 'agent'): ?>
                                            Rooms: <?php echo $rooms_count; ?> × Nights: <?php echo $nights_to_use; ?><br>
                                            <?php endif; ?>
                                            Subtotal: LKR <?php echo number_format($rate_data['total'], 2); ?>
                                            <?php if (isset($rate_data['discount_amount'])): ?>
                                                <br>Discount (<?php echo $discount_rate; ?>%): -LKR <?php echo number_format($rate_data['discount_amount'], 2); ?>
                                                <br><strong>Total: LKR <?php echo number_format($rate_data['discounted_total'], 2); ?></strong>
                                            <?php else: ?>
                                                <br><strong>Total: LKR <?php echo number_format($rate_data['total'], 2); ?></strong>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                    <?php
                                    $first_option = false;
                                    endforeach;
                                    ?>
                                </div>
                            </div>

                            <?php if ($booking_type === 'agent'): ?>
                            <!-- Travel Agent Billing Note -->
                            <div class="agent-billing-note">
                                <h6><i class="fa fa-building"></i> Travel Agent Billing</h6>
                                <p><strong>Company Billing Information:</strong></p>
                                <p><?php echo nl2br(htmlspecialchars($billing_info)); ?></p>
                                <p class="text-muted"><small><i class="fa fa-info-circle"></i> This reservation will be billed directly to your travel company as per your contract terms.</small></p>
                            </div>
                            <?php else: ?>
                            <!-- Guest Payment Options -->
                            <div class="payment-options">
                                <h6><i class="fa fa-credit-card"></i> Payment Options</h6>

                                <div class="alert alert-info">
                                    <h6><i class="fa fa-info-circle"></i> Choose Your Payment Method</h6>
                                    <p><strong>Option 1:</strong> Enter credit card details for guaranteed reservation</p>
                                    <p><strong>Option 2:</strong> Proceed without credit card (temporary hold - must pay by 7 PM daily)</p>
                                </div>

                                <div class="payment-option selected" data-payment="with-card">
                                    <input type="radio" name="payment_method" value="with-card" checked>
                                    <label>
                                        <strong><i class="fa fa-credit-card"></i> Pay with Credit Card (Recommended)</strong><br>
                                        <small>Guaranteed reservation, no daily cancellation risk</small>
                                    </label>
                                </div>

                                <div class="payment-option" data-payment="without-card">
                                    <input type="radio" name="payment_method" value="without-card">
                                    <label>
                                        <strong><i class="fa fa-clock-o"></i> Reserve without Credit Card</strong><br>
                                        <small>Temporary hold - reservation will be canceled at 7 PM daily if not paid</small>
                                    </label>
                                </div>

                                <!-- Credit Card Section -->
                                <div class="credit-card-section show" id="creditCardSection">
                                    <div class="credit-card-header">
                                        <i class="fa fa-credit-card"></i>
                                        <h6>Credit Card Details</h6>
                                    </div>

                                    <div class="card-form-row">
                                        <div class="card-form-group full-width">
                                            <label for="cardNumber">Card Number</label>
                                            <input type="text" class="card-input card-number-input" id="cardNumber" name="card_number"
                                                   placeholder="1234 5678 9012 3456" maxlength="19" required>
                                            <div class="card-icons">
                                                <div class="card-icon visa">VISA</div>
                                                <div class="card-icon mastercard">MC</div>
                                                <div class="card-icon amex">AMEX</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card-form-row">
                                        <div class="card-form-group">
                                            <label for="cardName">Name on Card</label>
                                            <input type="text" class="card-input" id="cardName" name="card_name"
                                                   placeholder="John Doe" required>
                                        </div>
                                    </div>

                                    <div class="card-form-row">
                                        <div class="card-form-group">
                                            <label for="expiryDate">Expiry Date</label>
                                            <input type="text" class="card-input" id="expiryDate" name="expiry_date"
                                                   placeholder="MM/YY" maxlength="5" required>
                                        </div>
                                        <div class="card-form-group">
                                            <label for="cvv">CVV</label>
                                            <input type="text" class="card-input" id="cvv" name="cvv"
                                                   placeholder="123" maxlength="4" required>
                                        </div>
                                    </div>

                                    <div class="security-note">
                                        <i class="fa fa-shield"></i>
                                        <div>
                                            <p class="security-text">
                                                <strong>Secure Payment:</strong> Your payment information is encrypted and protected with industry-standard SSL security.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Confirmation Buttons -->
                            <div class="text-center" style="margin-top: 30px;">
                                <a href="index.php" class="btn btn-secondary" style="margin-right: 15px;">Cancel</a>
                                <button type="submit" class="btn-confirm">
                                    <i class="fa fa-check"></i> Confirm Reservation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Payment Section End -->

    <!-- Js Plugins -->
    <script src="js/jquery-3.3.1.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>

    <script>
        $(document).ready(function() {
            // Handle rate option selection
            $('.rate-option').click(function() {
                $('.rate-option').removeClass('selected');
                $(this).addClass('selected');
                $(this).find('input[type="radio"]').prop('checked', true);
                updateTotalAmount();
            });

            // Handle payment option selection
            $('.payment-option').click(function() {
                $('.payment-option').removeClass('selected');
                $(this).addClass('selected');
                $(this).find('input[type="radio"]').prop('checked', true);

                const paymentMethod = $(this).data('payment');
                const creditCardSection = $('#creditCardSection');

                if (paymentMethod === 'with-card') {
                    creditCardSection.addClass('show');
                    $('#cardNumber, #cardName, #expiryDate, #cvv').prop('required', true);
                } else {
                    creditCardSection.removeClass('show');
                    $('#cardNumber, #cardName, #expiryDate, #cvv').prop('required', false);
                }
            });

            // Credit card number formatting
            $('#cardNumber').on('input', function() {
                let value = $(this).val().replace(/\s/g, '').replace(/[^0-9]/gi, '');
                let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                if (formattedValue !== $(this).val()) {
                    $(this).val(formattedValue);
                }
            });

            // Expiry date formatting
            $('#expiryDate').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                $(this).val(value);
            });

            // CVV validation
            $('#cvv').on('input', function() {
                let value = $(this).val().replace(/[^0-9]/g, '');
                $(this).val(value);
            });

            // Card name validation
            $('#cardName').on('input', function() {
                let value = $(this).val().replace(/[^a-zA-Z\s]/g, '');
                $(this).val(value);
            });

            // Update total amount based on selected rate
            function updateTotalAmount() {
                const selectedRate = $('.rate-option.selected');
                const totalText = selectedRate.find('strong:last').text();
                const totalAmount = totalText.replace(/[^\d.]/g, '');

                // Add hidden field for total amount
                $('input[name="total_amount"]').remove();
                $('<input>').attr({
                    type: 'hidden',
                    name: 'total_amount',
                    value: totalAmount
                }).appendTo('#paymentForm');
            }

            // Initialize total amount
            updateTotalAmount();

            // Handle form submission
            $('#paymentForm').submit(function(e) {
                const paymentMethod = $('input[name="payment_method"]:checked').val();

                if (paymentMethod === 'with-card') {
                    // Validate credit card fields
                    const cardNumber = $('#cardNumber').val().replace(/\s/g, '');
                    const cardName = $('#cardName').val().trim();
                    const expiryDate = $('#expiryDate').val();
                    const cvv = $('#cvv').val();

                    let errors = [];

                    if (!cardNumber || cardNumber.length < 13) {
                        errors.push('Please enter a valid card number');
                    }

                    if (!cardName) {
                        errors.push('Please enter the name on card');
                    }

                    if (!expiryDate || !expiryDate.match(/^\d{2}\/\d{2}$/)) {
                        errors.push('Please enter a valid expiry date (MM/YY)');
                    }

                    if (!cvv || cvv.length < 3) {
                        errors.push('Please enter a valid CVV');
                    }

                    if (errors.length > 0) {
                        e.preventDefault();
                        alert('Please fix the following errors:\n\n' + errors.join('\n'));
                        return false;
                    }

                    // Combine credit card details for backend processing
                    const creditCardDetails = `Card Number: ${cardNumber}\nName on Card: ${cardName}\nExpiry Date: ${expiryDate}\nCVV: ${cvv}`;
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'credit_card_details',
                        value: creditCardDetails
                    }).appendTo('#paymentForm');
                }

                // Show loading state
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Processing...');

                return true;
            });
        });
    </script>
</body>
</html>
