<?php
session_start();
require_once('../config/db.php');

$error = '';
$user_type = $_GET['type'] ?? 'customer'; // customer or agent

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    $login_type = $_POST['user_type'];
    
    if ($login_type === 'customer') {
        // Check customers table
        $query = "SELECT CustomerID, FirstName, LastName, Email, PasswordHash FROM Customers WHERE Email = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($user = $result->fetch_assoc()) {
            if (password_verify($password, $user['PasswordHash'])) {
                $_SESSION['user_id'] = $user['CustomerID'];
                $_SESSION['user_name'] = $user['FirstName'] . ' ' . $user['LastName'];
                $_SESSION['user_email'] = $user['Email'];
                $_SESSION['user_type'] = 'customer';
                
                header('Location: ../profile/customer.php');
                exit();
            } else {
                $error = 'Invalid password';
            }
        } else {
            $error = 'Email not found';
        }
    } else {
        // Check travel companies table
        $query = "SELECT CompanyID, CompanyName, ContactEmail, PasswordHash FROM TravelCompanies WHERE ContactEmail = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($company = $result->fetch_assoc()) {
            if (password_verify($password, $company['PasswordHash'])) {
                $_SESSION['user_id'] = $company['CompanyID'];
                $_SESSION['user_name'] = $company['CompanyName'];
                $_SESSION['user_email'] = $company['ContactEmail'];
                $_SESSION['user_type'] = 'agent';
                
                header('Location: ../profile/agent.php');
                exit();
            } else {
                $error = 'Invalid password';
            }
        } else {
            $error = 'Email not found';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #008080 0%, #1e3a8a 50%, #**********%);
        }
        
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo img {
            max-width: 200px;
            height: auto;
        }
        
        .form-control {
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .form-control:focus {
            border-color: #008080;
            box-shadow: none;
        }
        
        .login-btn {
            background: linear-gradient(135deg, #008080 0%, #1e3a8a 50%, #**********%);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            width: 100%;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .login-btn:hover {
            opacity: 0.9;
        }
        
        .error-message {
            color: #dc3545;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .user-type-selector {
            margin-bottom: 20px;
        }
        
        .user-type-selector .btn {
            width: 78%;
            margin: 1%;
            margin-left: 10%;
            
        }
        
        .user-type-selector .btn.active {
            background: linear-gradient(135deg, #008080 0%, #1e3a8a 50%, #**********%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-logo">
                <img src="../img/logo.png" alt="Echo Hotels">
            </div>
            
            <div class="user-type-selector">
                <button type="button" class="btn btn-outline-primary <?php echo $user_type === 'customer' ? 'active' : ''; ?>" onclick="setUserType('customer')">Customer</button>
                <button type="button" class="btn btn-outline-primary <?php echo $user_type === 'agent' ? 'active' : ''; ?>" onclick="setUserType('agent')">Travel Agent</button>
            </div>
            
            <?php if ($error): ?>
                <div class="error-message"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <form method="POST" action="login.php">
                <input type="hidden" name="user_type" id="user_type" value="<?php echo htmlspecialchars($user_type); ?>">
                
                <div class="form-group">
                    <input type="email" name="email" class="form-control" placeholder="Email" required>
                </div>
                
                <div class="form-group">
                    <input type="password" name="password" class="form-control" placeholder="Password" required>
                </div>
                
                <button type="submit" class="login-btn">Login</button>
            </form>
            
            <div class="text-center mt-3">
                <p>Don't have an account? <a href="signup.php" style="color:rgb(0, 139, 226); text-decoration: none;" onmouseover="this.style.color='#000'" onmouseout="this.style.color='#008080'">Sign up here</a></p>
                <p><a href="../index.php" style="color:rgb(0, 139, 226); text-decoration: none;" onmouseover="this.style.color='#000'" onmouseout="this.style.color='#008080'">Back to Home</a></p>
            </div>
        </div>
    </div>
    
    <script>
        function setUserType(type) {
            document.getElementById('user_type').value = type;
            document.querySelectorAll('.user-type-selector .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
    </script>
</body>
</html>

