<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo '<div class="alert alert-danger">Unauthorized access</div>';
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['billing_id'])) {
    $billing_id = intval($_POST['billing_id']);

    try {
        // Get detailed billing information
        $query = "
            SELECT b.*,
                   r.ReservationID,
                   r.StartDate,
                   r.EndDate,
                   r.Number<PERSON>fGuests,
                   CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
                   c.Email as CustomerEmail,
                   c.Phone as CustomerPhone,
                   h.Name as HotelName,
                   h.Location as HotelLocation,
                   rt.TypeName as RoomType,
                   rt.DailyRate,
                   rm.RoomN<PERSON>ber,
                   DATEDIFF(r.EndDate, r.StartDate) as StayDuration
            FROM Billing b
            LEFT JOIN Reservations r ON b.ReservationID = r.ReservationID
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            LEFT JOIN Hotels h ON r.HotelID = h.HotelID
            LEFT JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            LEFT JOIN CheckIns ci ON r.ReservationID = ci.ReservationID
            LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
            WHERE b.BillingID = ?
        ";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $billing_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($billing = $result->fetch_assoc()) {
            // Format dates
            $billing_date = date('F j, Y g:i A', strtotime($billing['BillingDateTime']));
            $start_date = $billing['StartDate'] ? date('F j, Y', strtotime($billing['StartDate'])) : 'N/A';
            $end_date = $billing['EndDate'] ? date('F j, Y', strtotime($billing['EndDate'])) : 'N/A';

            // Status badge color
            $status_class = $billing['PaymentStatus'] === 'Paid' ? 'success' :
                           ($billing['PaymentStatus'] === 'No-Show' ? 'danger' : 'warning');

            // Check if this is a no-show charge
            $is_no_show = $billing['PaymentMethod'] === 'No-Show Charge';

            echo '
            <div class="p-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-file-invoice-dollar mr-2"></i>Billing Information</h5>
                            </div>
                            <div class="card-body bg-white">
                                <table class="table table-borderless table-sm mb-0">
                                    <tr>
                                        <td width="40%" class="text-muted"><i class="fas fa-hashtag mr-2"></i>Billing ID:</td>
                                        <td><span class="badge badge-light">#' . htmlspecialchars($billing['BillingID']) . '</span></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted"><i class="fas fa-rupee-sign mr-2"></i>Total Amount:</td>
                                        <td><strong class="text-success h6">' . format_currency($billing['TotalAmount']) . '</strong></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted"><i class="fas fa-credit-card mr-2"></i>Payment Method:</td>
                                        <td><span class="badge badge-info">' . htmlspecialchars($billing['PaymentMethod']) . '</span></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted"><i class="fas fa-check-circle mr-2"></i>Payment Status:</td>
                                        <td><span class="badge badge-' . $status_class . '">' . htmlspecialchars($billing['PaymentStatus']) . '</span></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted"><i class="fas fa-calendar mr-2"></i>Billing Date:</td>
                                        <td>' . $billing_date . '</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-user-circle mr-2"></i>Customer Information</h5>
                            </div>
                            <div class="card-body bg-white">
                                <table class="table table-borderless table-sm mb-0">
                                    <tr>
                                        <td width="30%" class="text-muted"><i class="fas fa-user mr-2"></i>Name:</td>
                                        <td><strong>' . htmlspecialchars($billing['CustomerName'] ?: 'Walk-in Guest') . '</strong></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted"><i class="fas fa-envelope mr-2"></i>Email:</td>
                                        <td>' . htmlspecialchars($billing['CustomerEmail'] ?: 'N/A') . '</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted"><i class="fas fa-phone mr-2"></i>Phone:</td>
                                        <td>' . htmlspecialchars($billing['CustomerPhone'] ?: 'N/A') . '</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>';

            // Show no-show alert if applicable
            if ($is_no_show) {
                echo '
                <div class="px-4 pb-4">
                    <div class="alert alert-danger border-0 shadow-sm" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="text-white mb-2"><i class="fas fa-exclamation-triangle mr-2"></i>No-Show Charge</h5>
                                <p class="text-white mb-0">This is a no-show billing record. Customer failed to check-in for their reservation and has been charged the standard no-show fee of LKR 1,500.00 as per hotel policy.</p>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="bg-white rounded p-3">
                                    <h6 class="text-danger mb-1">No-Show Fee</h6>
                                    <h4 class="text-danger mb-0">LKR 1,500.00</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>';
            }

            echo '</div>';

            if ($billing['ReservationID']) {
                echo '
                <div class="px-4 pb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0"><i class="fas fa-calendar-check mr-2"></i>Reservation Details</h5>
                                </div>
                                <div class="card-body bg-white">
                                    <table class="table table-borderless table-sm mb-0">
                                        <tr>
                                            <td width="45%" class="text-muted"><i class="fas fa-hashtag mr-2"></i>Reservation ID:</td>
                                            <td><span class="badge badge-light">#' . htmlspecialchars($billing['ReservationID']) . '</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-sign-in-alt mr-2"></i>Check-in Date:</td>
                                            <td><span class="badge badge-success">' . $start_date . '</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-sign-out-alt mr-2"></i>Check-out Date:</td>
                                            <td><span class="badge badge-danger">' . $end_date . '</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-clock mr-2"></i>Duration:</td>
                                            <td><strong>' . ($billing['StayDuration'] ?: 'N/A') . ' night(s)</strong></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-users mr-2"></i>Guests:</td>
                                            <td><span class="badge badge-info">' . htmlspecialchars($billing['NumberOfGuests'] ?: '1') . ' Guest(s)</span></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0"><i class="fas fa-hotel mr-2"></i>Hotel & Room Details</h5>
                                </div>
                                <div class="card-body bg-white">
                                    <table class="table table-borderless table-sm mb-0">
                                        <tr>
                                            <td width="40%" class="text-muted"><i class="fas fa-building mr-2"></i>Hotel:</td>
                                            <td><strong>' . htmlspecialchars($billing['HotelName'] ?: 'N/A') . '</strong></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-map-marker-alt mr-2"></i>Location:</td>
                                            <td>' . htmlspecialchars($billing['HotelLocation'] ?: 'N/A') . '</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-bed mr-2"></i>Room Type:</td>
                                            <td><span class="badge badge-primary">' . htmlspecialchars($billing['RoomType'] ?: 'N/A') . '</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-door-open mr-2"></i>Room Number:</td>
                                            <td><span class="badge badge-secondary">' . htmlspecialchars($billing['RoomNumber'] ?: 'Not assigned') . '</span></td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted"><i class="fas fa-rupee-sign mr-2"></i>Daily Rate:</td>
                                            <td><strong class="text-success">' . format_currency($billing['DailyRate'] ?: 0) . '</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>';
            }

            // Show base room charges
            if ($billing['ReservationID'] && $billing['StayDuration']) {
                $room_total = $billing['StayDuration'] * $billing['DailyRate'];
                echo '
                <div class="px-4 pb-4">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-bed mr-2"></i>Room Charges Breakdown</h5>
                        </div>
                        <div class="card-body p-0 bg-white">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover table-sm mb-0">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th width="15%" class="text-center"><i class="fas fa-tag mr-1"></i>Item Type</th>
                                            <th width="35%"><i class="fas fa-info-circle mr-1"></i>Description</th>
                                            <th width="15%" class="text-center"><i class="fas fa-sort-numeric-up mr-1"></i>Nights</th>
                                            <th width="20%" class="text-right"><i class="fas fa-rupee-sign mr-1"></i>Rate per Night</th>
                                            <th width="15%" class="text-right"><i class="fas fa-calculator mr-1"></i>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-center"><span class="badge badge-primary">Room Charge</span></td>
                                            <td>' . htmlspecialchars($billing['RoomType']) . ' - Room ' . htmlspecialchars($billing['RoomNumber'] ?: 'TBD') . '</td>
                                            <td class="text-center"><span class="badge badge-light">' . $billing['StayDuration'] . '</span></td>
                                            <td class="text-right">' . format_currency($billing['DailyRate']) . '</td>
                                            <td class="text-right"><strong class="text-success">' . format_currency($room_total) . '</strong></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>';
            }

            // Get additional charges if available
            $charges_query = "
                SELECT
                    'Additional Charge' as ItemType,
                    Description as ItemDescription,
                    1 as Quantity,
                    Amount as UnitPrice
                FROM AdditionalCharges
                WHERE BillingID = ?
                ORDER BY ChargeDate DESC
            ";
            $charges_stmt = $conn->prepare($charges_query);
            $charges_stmt->bind_param("i", $billing_id);
            $charges_stmt->execute();
            $charges_result = $charges_stmt->get_result();

            if ($charges_result->num_rows > 0) {
                echo '
                <div class="px-4 pb-4">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-plus-circle mr-2"></i>Additional Charges</h5>
                        </div>
                        <div class="card-body p-0 bg-white">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover table-sm mb-0">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th width="15%" class="text-center"><i class="fas fa-tag mr-1"></i>Item Type</th>
                                            <th width="40%"><i class="fas fa-info-circle mr-1"></i>Description</th>
                                            <th width="15%" class="text-center"><i class="fas fa-sort-numeric-up mr-1"></i>Quantity</th>
                                            <th width="15%" class="text-right"><i class="fas fa-rupee-sign mr-1"></i>Unit Price</th>
                                            <th width="15%" class="text-right"><i class="fas fa-calculator mr-1"></i>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>';

                $subtotal = 0;
                while ($charge = $charges_result->fetch_assoc()) {
                    $item_total = $charge['Quantity'] * $charge['UnitPrice'];
                    $subtotal += $item_total;

                    echo '<tr>
                            <td class="text-center"><span class="badge badge-secondary">' . htmlspecialchars($charge['ItemType']) . '</span></td>
                            <td>' . htmlspecialchars($charge['ItemDescription']) . '</td>
                            <td class="text-center"><span class="badge badge-light">' . htmlspecialchars($charge['Quantity']) . '</span></td>
                            <td class="text-right">' . format_currency($charge['UnitPrice']) . '</td>
                            <td class="text-right"><strong class="text-success">' . format_currency($item_total) . '</strong></td>
                          </tr>';
                }

                echo '</tbody>
                                    <tfoot class="bg-light">
                                        <tr>
                                            <td colspan="4" class="text-right"><strong><i class="fas fa-calculator mr-2"></i>Additional Charges Subtotal:</strong></td>
                                            <td class="text-right"><strong class="text-success h6">' . format_currency($subtotal) . '</strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>';
            }

            // Show customer payment history if customer exists
            if ($billing['CustomerName'] && $billing['CustomerName'] !== 'Walk-in Guest') {
                $customer_query = "
                    SELECT b.BillingID, b.TotalAmount, b.PaymentMethod, b.PaymentStatus, b.BillingDateTime,
                           r.ReservationID, h.Name as HotelName
                    FROM Billing b
                    JOIN Reservations r ON b.ReservationID = r.ReservationID
                    JOIN Hotels h ON r.HotelID = h.HotelID
                    JOIN Customers c ON r.CustomerID = c.CustomerID
                    WHERE CONCAT(c.FirstName, ' ', c.LastName) = ?
                    AND b.BillingID != ?
                    ORDER BY b.BillingDateTime DESC
                    LIMIT 5
                ";
                $customer_stmt = $conn->prepare($customer_query);
                $customer_stmt->bind_param("si", $billing['CustomerName'], $billing_id);
                $customer_stmt->execute();
                $customer_result = $customer_stmt->get_result();

                if ($customer_result->num_rows > 0) {
                    echo '
                    <div class="px-4 pb-4">
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0"><i class="fas fa-history mr-2"></i>Customer Payment History</h5>
                            </div>
                            <div class="card-body p-0 bg-white">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-sm mb-0">
                                        <thead class="thead-light">
                                            <tr>
                                                <th width="15%">Bill ID</th>
                                                <th width="15%">Reservation</th>
                                                <th width="25%">Hotel</th>
                                                <th width="15%">Amount</th>
                                                <th width="15%">Method</th>
                                                <th width="15%">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>';

                    while ($history = $customer_result->fetch_assoc()) {
                        $status_badge = $history['PaymentStatus'] === 'Paid' ? 'success' :
                                       ($history['PaymentStatus'] === 'No-Show' ? 'danger' : 'warning');
                        $method_badge = $history['PaymentMethod'] === 'No-Show Charge' ? 'danger' : 'info';

                        echo '<tr>
                                <td><span class="badge badge-light">#' . $history['BillingID'] . '</span></td>
                                <td><span class="badge badge-light">#' . $history['ReservationID'] . '</span></td>
                                <td>' . htmlspecialchars($history['HotelName']) . '</td>
                                <td>' . format_currency($history['TotalAmount']) . '</td>
                                <td><span class="badge badge-' . $method_badge . '">' . htmlspecialchars($history['PaymentMethod']) . '</span></td>
                                <td><span class="badge badge-' . $status_badge . '">' . htmlspecialchars($history['PaymentStatus']) . '</span></td>
                              </tr>';
                    }

                    echo '</tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>';
                }
            }

            // Show grand total
            echo '
            <div class="px-4 pb-4">
                <div class="card border-0 text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);">
                    <div class="card-body text-center py-4">
                        <h4 class="mb-2"><i class="fas fa-receipt mr-2"></i>Grand Total</h4>
                        <h1 class="mb-0 font-weight-bold" style="font-size: 2.5rem;">' . format_currency($billing['TotalAmount']) . '</h1>
                        <p class="mb-0 mt-3" style="font-size: 1.1rem;">
                            <i class="fas fa-info-circle mr-1"></i>
                            Payment Status: <span class="badge badge-' . ($billing['PaymentStatus'] === 'Paid' ? 'light' : 'warning') . ' ml-1 px-3 py-2" style="font-size: 0.9rem;">' . htmlspecialchars($billing['PaymentStatus']) . '</span>
                        </p>
                    </div>
                </div>
            </div>';

        } else {
            echo '<div class="alert alert-warning">Billing record not found.</div>';
        }

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error loading billing details: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
} else {
    echo '<div class="alert alert-danger">Invalid request.</div>';
}

$conn->close();
?>
