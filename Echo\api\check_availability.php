<?php
session_start();
require_once('../config/db.php');

header('Content-Type: application/json');

// Debug logging
error_log("API called with POST data: " . print_r($_POST, true));

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get form data
$booking_type = $_POST['booking_type'] ?? '';
$location = $_POST['location'] ?? '';
$room_type = $_POST['room_type'] ?? '';
$check_in = $_POST['check_in'] ?? '';
$check_out = $_POST['check_out'] ?? '';
$guests = $_POST['guests'] ?? '';

// Travel agent specific fields
$company_name = $_POST['company_name'] ?? '';
$rooms_count = (int)($_POST['rooms_count'] ?? 1);
$nights = (int)($_POST['nights'] ?? 1);
$discount_rate = (int)($_POST['discount_rate'] ?? 0);
$billing_info = $_POST['billing_info'] ?? '';

// Validation
if (empty($location) || empty($room_type) || empty($check_in) || empty($check_out)) {
    echo json_encode(['error' => 'Please fill in all required fields']);
    exit();
}

// Validate dates
if (strtotime($check_in) >= strtotime($check_out)) {
    echo json_encode(['error' => 'Check-out date must be after check-in date']);
    exit();
}

if (strtotime($check_in) < strtotime(date('Y-m-d'))) {
    echo json_encode(['error' => 'Check-in date cannot be in the past']);
    exit();
}

// Map location to hotel ID
$location_map = [
    'kandy' => 1,
    'jaffna' => 2,
    'galle' => 3
];

$hotel_id = $location_map[$location] ?? 0;
if ($hotel_id === 0) {
    echo json_encode(['error' => 'Invalid location selected']);
    exit();
}

// Get hotel information
$hotel_query = "SELECT * FROM hotels WHERE HotelID = ?";
$stmt = $conn->prepare($hotel_query);
$stmt->bind_param("i", $hotel_id);
$stmt->execute();
$hotel_result = $stmt->get_result();
$hotel = $hotel_result->fetch_assoc();

if (!$hotel) {
    echo json_encode(['error' => 'Hotel not found']);
    exit();
}

// Get room type information
$room_type_query = "SELECT * FROM roomtypes WHERE TypeName = ?";
$stmt = $conn->prepare($room_type_query);
$stmt->bind_param("s", $room_type);
$stmt->execute();
$room_type_result = $stmt->get_result();
$room_type_data = $room_type_result->fetch_assoc();

if (!$room_type_data) {
    echo json_encode(['error' => 'Room type not found']);
    exit();
}

// Check room availability
// First, get total rooms of this type at this hotel
$total_rooms_query = "SELECT COUNT(*) as total_rooms FROM rooms WHERE HotelID = ? AND RoomTypeID = ? AND Status = 'Available'";
$stmt = $conn->prepare($total_rooms_query);
$stmt->bind_param("ii", $hotel_id, $room_type_data['RoomTypeID']);
$stmt->execute();
$total_rooms_result = $stmt->get_result();
$total_rooms = $total_rooms_result->fetch_assoc()['total_rooms'];

// Then, check how many are reserved for the requested dates
$reserved_rooms_query = "
    SELECT COUNT(*) as reserved_rooms
    FROM reservations res
    WHERE res.HotelID = ? AND res.RoomTypeID = ? AND res.Status = 'Confirmed'
    AND (
        (? < res.EndDate AND ? > res.StartDate)
    )
";

$stmt = $conn->prepare($reserved_rooms_query);
$stmt->bind_param("iiss", $hotel_id, $room_type_data['RoomTypeID'], $check_in, $check_out);
$stmt->execute();
$reserved_rooms_result = $stmt->get_result();
$reserved_rooms = $reserved_rooms_result->fetch_assoc()['reserved_rooms'];

$available_rooms = $total_rooms - $reserved_rooms;

// Calculate pricing
$check_in_date = new DateTime($check_in);
$check_out_date = new DateTime($check_out);
$nights_count = $check_in_date->diff($check_out_date)->days;

$daily_rate = $room_type_data['DailyRate'];
$weekly_rate = $room_type_data['WeeklyRate'];
$monthly_rate = $room_type_data['MonthlyRate'];

// Calculate rate options for guest booking (single room)
$rate_options = [];
if ($room_type === 'Residential Suite') {
    $rate_options = [
        'daily' => [
            'rate' => $daily_rate,
            'total' => $daily_rate * $nights_count,
            'label' => 'Daily Rate'
        ]
    ];

    if ($nights_count >= 7 && $weekly_rate) {
        $weeks = ceil($nights_count / 7);
        $rate_options['weekly'] = [
            'rate' => $weekly_rate,
            'total' => $weekly_rate * $weeks,
            'label' => 'Weekly Rate'
        ];
    }

    if ($nights_count >= 30 && $monthly_rate) {
        $months = ceil($nights_count / 30);
        $rate_options['monthly'] = [
            'rate' => $monthly_rate,
            'total' => $monthly_rate * $months,
            'label' => 'Monthly Rate'
        ];
    }
} else {
    $rate_options['daily'] = [
        'rate' => $daily_rate,
        'total' => $daily_rate * $nights_count,
        'label' => 'Daily Rate'
    ];
}

// Check if user is logged in
$user_logged_in = isset($_SESSION['user_id']);
$user_type = $_SESSION['user_type'] ?? null;

// Validate guest booking requirements
if ($available_rooms < 1) {
    echo json_encode([
        'error' => 'No rooms available for the selected dates',
        'available_rooms' => $available_rooms
    ]);
    exit();
}

// Prepare response for guest booking
$response = [
    'success' => true,
    'hotel' => $hotel,
    'room_type' => $room_type_data,
    'available_rooms' => $available_rooms,
    'nights' => $nights_count,
    'check_in' => $check_in,
    'check_out' => $check_out,
    'booking_type' => 'guest',
    'rate_options' => $rate_options,
    'user_logged_in' => $user_logged_in,
    'user_type' => $user_type,
    'guests' => $guests
];

echo json_encode($response);
?>
