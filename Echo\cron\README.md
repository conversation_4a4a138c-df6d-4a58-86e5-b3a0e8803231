# Echo Hotel Auto-Cancellation System

## 🎯 Overview

This system automatically cancels reservations made without credit card details every day at 7:00 PM, as per hotel policy. It also handles no-show billing and maintains comprehensive logs.

## 📋 Features

- **Automatic Cancellation**: Cancels reservations without credit card details at 7 PM daily
- **No-Show Billing**: Creates billing records for customers who don't show up (LKR 1,500 fee)
- **Comprehensive Logging**: Tracks all activities with timestamps
- **Email Notifications**: Sends cancellation and billing notices (configurable)
- **Web Monitoring**: Real-time dashboard for system monitoring
- **Cross-Platform**: Works on Windows (Task Scheduler) and Linux (Cron)

## 🚀 Quick Setup (Windows with XAMPP)

### Step 1: Automatic Setup
1. Navigate to the `Echo/cron/` folder
2. Right-click on `setup_windows_scheduler.bat`
3. Select **"Run as administrator"**
4. Follow the on-screen instructions

### Step 2: Verify Setup
1. Open your browser and go to: `http://localhost/Echo%20Final/Echo/cron/test_auto_cancel.php`
2. Check if the scheduled task is configured properly
3. Use the "Run Cleanup Script Now" button to test manually

### Step 3: Monitor System
- **Admin Dashboard**: `http://localhost/Echo%20Final/Echo/admin/auto_cancel_monitor.php`
- **Test Interface**: `http://localhost/Echo%20Final/Echo/cron/test_auto_cancel.php`
- **Log File**: `Echo/cron/cleanup_log.txt`

## 🔧 Manual Setup (Alternative)

### Windows Task Scheduler
```batch
# Open Command Prompt as Administrator and run:
schtasks /create /tn "Echo_Hotel_Daily_Cleanup" /tr "\"C:\xampp\php\php.exe\" \"C:\xampp\htdocs\Echo Final\Echo\cron\daily_cleanup.php\"" /sc daily /st 19:00 /ru SYSTEM /f
```

### Linux Cron Job
```bash
# Add to crontab (crontab -e):
0 19 * * * /usr/bin/php /path/to/your/project/Echo/cron/daily_cleanup.php
```

## 📊 How It Works

### 1. Reservation Cancellation Logic
- **Target**: Reservations without credit card details
- **Timing**: Daily at 7:00 PM
- **Criteria**:
  - Status = 'Confirmed'
  - No credit card details provided
  - Customer reservations only (not travel agent bookings)
  - Created today and it's past 7 PM, OR created before today

### 2. No-Show Billing Logic
- **Target**: Confirmed reservations where check-in date has passed
- **Action**: Create billing record with LKR 1,500 no-show fee
- **Status Update**: Change reservation status to 'No-Show'

### 3. Notification System
- **Cancellation Email**: Sent to customers whose reservations are cancelled
- **No-Show Email**: Sent to customers/companies with no-show billing
- **Admin Logs**: Comprehensive logging for system administrators

## 📁 File Structure

```
Echo/cron/
├── daily_cleanup.php           # Main cleanup script
├── setup_windows_scheduler.bat # Windows setup script
├── test_auto_cancel.php        # Manual testing interface
├── cleanup_log.txt            # Activity log file (created automatically)
└── README.md                  # This documentation

Echo/admin/
└── auto_cancel_monitor.php    # Admin monitoring dashboard
```

## 🛠️ Configuration

### Email Notifications
To enable email notifications, edit `daily_cleanup.php`:

```php
// Uncomment these lines in the email functions:
// mail($to, $subject, $message);
```

### Timezone Settings
The system is configured for Sri Lanka timezone (`Asia/Colombo`). To change:

```php
date_default_timezone_set('Your/Timezone');
```

### No-Show Fee Amount
To change the no-show fee (currently LKR 1,500):

```php
// In createNoShowBillingRecords() function:
$total_amount = 1500.00; // Change this value
```

## 🔍 Monitoring & Troubleshooting

### Check System Status
1. **Web Interface**: Visit the test page to see current status
2. **Log File**: Check `cleanup_log.txt` for detailed activity logs
3. **Database**: Query the `Reservations` table for cancelled reservations

### Common Issues

#### Task Not Running
- **Windows**: Check if scheduled task exists in Task Scheduler
- **Permissions**: Ensure the task runs with appropriate permissions
- **PHP Path**: Verify PHP executable path in the scheduled task

#### Script Errors
- **Database Connection**: Check database credentials in `config/db.php`
- **File Permissions**: Ensure log file can be created/written
- **PHP Extensions**: Verify required PHP extensions are installed

### Manual Testing
```bash
# Run the cleanup script manually:
php daily_cleanup.php

# Check scheduled task status (Windows):
schtasks /query /tn "Echo_Hotel_Daily_Cleanup"

# Run scheduled task manually (Windows):
schtasks /run /tn "Echo_Hotel_Daily_Cleanup"
```

## 📈 Monitoring Dashboard

The admin monitoring dashboard provides:
- **Real-time Statistics**: Current reservations without credit cards
- **System Status**: Whether auto-cancellation is currently active
- **Daily Summaries**: Cancellations and no-shows for today
- **Log Viewer**: Recent system activity

Access: `http://localhost/Echo%20Final/Echo/admin/auto_cancel_monitor.php`

## 🔒 Security Considerations

- **Admin Access**: Monitoring dashboard requires admin/manager login
- **Log Files**: Contain sensitive customer information - secure appropriately
- **Email Content**: Review email templates before enabling notifications
- **Database Access**: Ensure proper database user permissions

## 📞 Support

For issues or questions:
1. Check the log file for error messages
2. Use the test interface to verify system status
3. Review this documentation for troubleshooting steps
4. Contact system administrator if problems persist

---

**Last Updated**: 2025-06-20  
**Version**: 1.0  
**Compatible**: Windows (XAMPP), Linux (LAMP/LEMP)
