<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';
$reservation_id = $_GET['reservation_id'] ?? '';

// Handle check-in process
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reservation_id = $_POST['reservation_id'];
    $customer_id = !empty($_POST['customer_id']) ? $_POST['customer_id'] : null;
    $room_id = $_POST['room_id'];
    $expected_checkout = $_POST['expected_checkout'];
    $special_requests = $_POST['special_requests'] ?? '';

    try {
        $conn->begin_transaction();

        // Verify reservation exists and is valid
        $verify_query = "
            SELECT r.*, rt.TypeName
            FROM Reservations r
            JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            WHERE r.ReservationID = ? AND r.HotelID = ? AND r.Status = 'Confirmed'
        ";
        $stmt = $conn->prepare($verify_query);
        $stmt->bind_param("ii", $reservation_id, $clerk_hotel_id);
        $stmt->execute();
        $reservation = $stmt->get_result()->fetch_assoc();

        if (!$reservation) {
            throw new Exception('Invalid reservation or reservation not found');
        }

        // Use CustomerID from reservation if not provided in form
        if ($customer_id === null) {
            $customer_id = $reservation['CustomerID'];
        }

        // If still no CustomerID (walk-in guest without customer record), create one
        if ($customer_id === null) {
            // Generate a unique email for walk-in guest to avoid NULL constraint issues
            $unique_email = 'walkin_' . uniqid() . '_' . time() . '@temp.local';

            // Create a temporary customer record for walk-in guest
            $temp_customer_query = "
                INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt)
                VALUES ('Walk-in', 'Guest', ?, NULL, ?, NOW(), NOW())
            ";
            $temp_password = password_hash('walkin123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare($temp_customer_query);
            $stmt->bind_param("ss", $unique_email, $temp_password);
            $stmt->execute();
            $customer_id = $conn->insert_id;

            // Update the reservation with the new customer ID
            $update_reservation_customer_query = "UPDATE Reservations SET CustomerID = ? WHERE ReservationID = ?";
            $stmt = $conn->prepare($update_reservation_customer_query);
            $stmt->bind_param("ii", $customer_id, $reservation_id);
            $stmt->execute();
        }

        // Check if room is available
        $room_check_query = "SELECT Status FROM Rooms WHERE RoomID = ? AND HotelID = ?";
        $stmt = $conn->prepare($room_check_query);
        $stmt->bind_param("ii", $room_id, $clerk_hotel_id);
        $stmt->execute();
        $room_status = $stmt->get_result()->fetch_assoc();

        if (!$room_status || $room_status['Status'] !== 'Available') {
            throw new Exception('Selected room is not available');
        }

        // Create check-in record (CustomerID is guaranteed to exist now)
        $checkin_query = "
            INSERT INTO CheckIns (
                ReservationID, CustomerID, HotelID, RoomID,
                CheckInDateTime, ExpectedCheckOutDate, CreatedAt, UpdatedAt
            ) VALUES (?, ?, ?, ?, NOW(), ?, NOW(), NOW())
        ";
        $stmt = $conn->prepare($checkin_query);
        $stmt->bind_param("iiiis", $reservation_id, $customer_id, $clerk_hotel_id, $room_id, $expected_checkout);

        if (!$stmt->execute()) {
            throw new Exception('Failed to create check-in record');
        }

        // Update reservation status
        $update_reservation_query = "
            UPDATE Reservations
            SET Status = 'CheckedIn', SpecialRequests = ?, UpdatedAt = NOW()
            WHERE ReservationID = ?
        ";
        $stmt = $conn->prepare($update_reservation_query);
        $stmt->bind_param("si", $special_requests, $reservation_id);
        $stmt->execute();

        // Update room status
        $update_room_query = "UPDATE Rooms SET Status = 'Occupied' WHERE RoomID = ?";
        $stmt = $conn->prepare($update_room_query);
        $stmt->bind_param("i", $room_id);
        $stmt->execute();

        $conn->commit();
        $message = 'Guest checked in successfully!';
        $message_type = 'success';

    } catch (Exception $e) {
        $conn->rollback();
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get pending check-ins for today
$today = date('Y-m-d');
$pending_query = "
    SELECT r.*,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CustomerName,
           c.Email, c.Phone,
           rt.TypeName as RoomType,
           h.Name as HotelName
    FROM Reservations r
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    JOIN Hotels h ON r.HotelID = h.HotelID
    WHERE r.HotelID = ? AND r.StartDate <= ? AND r.Status = 'Confirmed'
    AND NOT EXISTS (SELECT 1 FROM CheckIns ci WHERE ci.ReservationID = r.ReservationID)
    ORDER BY r.StartDate ASC
";
$stmt = $conn->prepare($pending_query);
$stmt->bind_param("is", $clerk_hotel_id, $today);
$stmt->execute();
$pending_checkins = $stmt->get_result();

// Get available rooms by type
$rooms_query = "
    SELECT r.RoomID, r.RoomNumber, rt.TypeName, rt.RoomTypeID
    FROM Rooms r
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.HotelID = ? AND r.Status = 'Available'
    ORDER BY rt.TypeName, r.RoomNumber
";
$stmt = $conn->prepare($rooms_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$available_rooms = $stmt->get_result();

// Group rooms by type
$rooms_by_type = [];
while ($room = $available_rooms->fetch_assoc()) {
    $rooms_by_type[$room['RoomTypeID']][] = $room;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check-In - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Guest Check-In</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <!-- Pending Check-ins -->
                <div class="card">
                    <div class="card-header">
                        <h3>Pending Check-ins</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Reservation ID</th>
                                        <th>Guest Name</th>
                                        <th>Contact</th>
                                        <th>Room Type</th>
                                        <th>Check-in Date</th>
                                        <th>Check-out Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($pending_checkins->num_rows > 0): ?>
                                        <?php while ($reservation = $pending_checkins->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $reservation['ReservationID']; ?></td>
                                            <td><?php echo htmlspecialchars($reservation['CustomerName']); ?></td>
                                            <td>
                                                <?php if ($reservation['Email']): ?>
                                                    <?php echo htmlspecialchars($reservation['Email']); ?><br>
                                                <?php endif; ?>
                                                <?php if ($reservation['Phone']): ?>
                                                    <?php echo htmlspecialchars($reservation['Phone']); ?>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($reservation['RoomType']); ?></td>
                                            <td><?php echo $reservation['StartDate']; ?></td>
                                            <td><?php echo $reservation['EndDate']; ?></td>
                                            <td>
                                                <button class="btn btn-primary btn-sm"
                                                        onclick="startCheckin(<?php echo $reservation['ReservationID']; ?>,
                                                                              '<?php echo htmlspecialchars($reservation['CustomerName']); ?>',
                                                                              <?php echo $reservation['CustomerID'] ?? 'null'; ?>,
                                                                              <?php echo $reservation['RoomTypeID']; ?>,
                                                                              '<?php echo $reservation['EndDate']; ?>')">
                                                    <i class="fa fa-sign-in"></i> Check-In
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No pending check-ins for today</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Check-in Modal -->
    <div class="modal fade" id="checkinModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Check-In Guest</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" id="reservation_id" name="reservation_id">
                        <input type="hidden" id="customer_id" name="customer_id">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Guest Name</label>
                                    <input type="text" id="guest_name" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Expected Check-out Date</label>
                                    <input type="date" id="expected_checkout" name="expected_checkout" class="form-control" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Select Room</label>
                            <select id="room_id" name="room_id" class="form-control" required>
                                <option value="">Select a room...</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Special Requests/Notes</label>
                            <textarea name="special_requests" class="form-control" rows="3"
                                      placeholder="Any special requests or notes..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-sign-in"></i> Check-In Guest
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        const roomsByType = <?php echo json_encode($rooms_by_type); ?>;

        function startCheckin(reservationId, guestName, customerId, roomTypeId, checkoutDate) {
            document.getElementById('reservation_id').value = reservationId;
            document.getElementById('customer_id').value = customerId || '';
            document.getElementById('guest_name').value = guestName;
            document.getElementById('expected_checkout').value = checkoutDate;

            // Populate room dropdown with rooms of the correct type
            const roomSelect = document.getElementById('room_id');
            roomSelect.innerHTML = '<option value="">Select a room...</option>';

            if (roomsByType[roomTypeId]) {
                roomsByType[roomTypeId].forEach(room => {
                    const option = document.createElement('option');
                    option.value = room.RoomID;
                    option.textContent = `${room.RoomNumber} (${room.TypeName})`;
                    roomSelect.appendChild(option);
                });
            }

            $('#checkinModal').modal('show');
        }
    </script>
</body>
</html>
