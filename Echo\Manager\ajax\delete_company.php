<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $company_id = intval($_POST['company_id']);
    
    // Validate input
    if (empty($company_id)) {
        echo json_encode(['success' => false, 'message' => 'Company ID is required']);
        exit();
    }
    
    try {
        // Check if company exists
        $check_query = "SELECT CompanyID, CompanyName FROM TravelCompanies WHERE CompanyID = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $company_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'Company not found']);
            exit();
        }
        
        $company = $check_result->fetch_assoc();
        
        // Check if company has reservations
        $reservations_query = "SELECT COUNT(*) as reservation_count FROM Reservations WHERE CompanyID = ?";
        $reservations_stmt = $conn->prepare($reservations_query);
        $reservations_stmt->bind_param("i", $company_id);
        $reservations_stmt->execute();
        $reservations_result = $reservations_stmt->get_result();
        $reservation_count = $reservations_result->fetch_assoc()['reservation_count'];
        
        if ($reservation_count > 0) {
            echo json_encode(['success' => false, 'message' => 'Cannot delete company with existing reservations. Please cancel or transfer reservations first.']);
            exit();
        }
        
        // Delete company
        $delete_query = "DELETE FROM TravelCompanies WHERE CompanyID = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $company_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => 'Travel company deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Company not found']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Error deleting company: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
