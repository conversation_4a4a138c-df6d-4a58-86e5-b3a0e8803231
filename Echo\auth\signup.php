<?php
session_start();
require_once('../config/db.php');

$message = '';
$message_type = '';
$signup_type = $_GET['type'] ?? 'customer'; // customer or agent

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_type = $_POST['user_type'] ?? '';

    if ($user_type === 'customer') {
        // Customer signup
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        // Validation
        if ($password !== $confirm_password) {
            $message = 'Passwords do not match';
            $message_type = 'error';
        } else {
            // Check if email already exists
            $check_query = "SELECT CustomerID FROM Customers WHERE Email = ?";
            $stmt = $conn->prepare($check_query);
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $message = 'Email already registered';
                $message_type = 'error';
            } else {
                // Insert new customer
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                $insert_query = "INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt) VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param("sssss", $first_name, $last_name, $email, $phone, $password_hash);

                if ($stmt->execute()) {
                    // Auto-login the user
                    $customer_id = $conn->insert_id;
                    $_SESSION['user_id'] = $customer_id;
                    $_SESSION['user_type'] = 'customer';
                    $_SESSION['user_name'] = $first_name . ' ' . $last_name;

                    // Check if this is from booking flow
                    if (isset($_POST['return_to']) && $_POST['return_to'] === 'booking') {
                        // Store booking data in session and redirect to payment
                        $_SESSION['booking_data'] = [
                            'hotel_id' => $_POST['hotel_id'] ?? '',
                            'room_type_id' => $_POST['room_type_id'] ?? '',
                            'check_in' => $_POST['check_in'] ?? '',
                            'check_out' => $_POST['check_out'] ?? '',
                            'guests' => $_POST['guests'] ?? '',
                            'booking_type' => $_POST['booking_type'] ?? 'guest',
                            'rate_type' => $_POST['rate_type'] ?? 'daily',
                            'company_name' => $_POST['company_name'] ?? '',
                            'rooms_count' => $_POST['rooms_count'] ?? 1,
                            'nights' => $_POST['nights'] ?? 1,
                            'discount_rate' => $_POST['discount_rate'] ?? 0,
                            'billing_info' => $_POST['billing_info'] ?? ''
                        ];
                        header('Location: ../payment_from_signup.php');
                        exit();
                    }

                    $message = 'Registration successful! You are now logged in.';
                    $message_type = 'success';
                } else {
                    $message = 'Registration failed. Please try again.';
                    $message_type = 'error';
                }
            }
        }
    } else {
        // Travel agent signup
        $company_name = sanitize_input($_POST['company_name'] ?? '');
        $contact_email = sanitize_input($_POST['contact_email'] ?? '');
        $contact_phone = sanitize_input($_POST['contact_phone'] ?? '');
        $address = sanitize_input($_POST['address'] ?? '');
        $contract_details = sanitize_input($_POST['contract_details'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        // Validation
        if ($password !== $confirm_password) {
            $message = 'Passwords do not match';
            $message_type = 'error';
        } else {
            // Check if email already exists
            $check_query = "SELECT CompanyID FROM TravelCompanies WHERE ContactEmail = ?";
            $stmt = $conn->prepare($check_query);
            $stmt->bind_param("s", $contact_email);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $message = 'Email already registered';
                $message_type = 'error';
            } else {
                // Insert new travel company
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                $insert_query = "INSERT INTO TravelCompanies (CompanyName, ContactEmail, ContactPhone, Address, ContractDetails, PasswordHash, CreatedAt, UpdatedAt) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param("ssssss", $company_name, $contact_email, $contact_phone, $address, $contract_details, $password_hash);

                if ($stmt->execute()) {
                    // Auto-login the user
                    $company_id = $conn->insert_id;
                    $_SESSION['user_id'] = $company_id;
                    $_SESSION['user_type'] = 'agent';
                    $_SESSION['user_name'] = $company_name;

                    // Check if this is from booking flow
                    if (isset($_POST['return_to']) && $_POST['return_to'] === 'booking') {
                        // Store booking data in session and redirect to payment
                        $_SESSION['booking_data'] = [
                            'hotel_id' => $_POST['hotel_id'] ?? '',
                            'room_type_id' => $_POST['room_type_id'] ?? '',
                            'check_in' => $_POST['check_in'] ?? '',
                            'check_out' => $_POST['check_out'] ?? '',
                            'guests' => $_POST['guests'] ?? '',
                            'booking_type' => $_POST['booking_type'] ?? 'agent',
                            'rate_type' => $_POST['rate_type'] ?? 'daily',
                            'company_name' => $_POST['company_name'] ?? '',
                            'rooms_count' => $_POST['rooms_count'] ?? 3,
                            'nights' => $_POST['nights'] ?? 1,
                            'discount_rate' => $_POST['discount_rate'] ?? 10,
                            'billing_info' => $_POST['billing_info'] ?? ''
                        ];
                        header('Location: ../payment_from_signup.php');
                        exit();
                    }

                    $message = 'Registration successful! You are now logged in.';
                    $message_type = 'success';
                } else {
                    $message = 'Registration failed. Please try again.';
                    $message_type = 'error';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .signup-container {
            min-height: 100vh;
            padding: 50px 0;
            background: linear-gradient(135deg, #008080 0%, #1e3a8a 50%, #047857 100%);
        }

        .signup-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            max-width: 600px;
            margin: 0 auto;
        }

        .signup-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .signup-logo img {
            max-width: 200px;
            height: auto;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .form-control:focus {
            border-color: #008080;
            box-shadow: none;
        }

        .signup-btn {
            background: linear-gradient(135deg, #008080 0%, #1e3a8a 50%, #047857 100%);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 5px;
            width: 100%;
            font-weight: bold;
            margin-top: 10px;
        }

        .signup-btn:hover {
            opacity: 0.9;
        }

        .user-type-selector {
            margin-bottom: 30px;
        }

        .user-type-selector .btn {
            width: 48%;
            margin: 1%;
        }

        .user-type-selector .btn.active {
            background: linear-gradient(135deg, #008080 0%, #1e3a8a 50%, #047857 100%);
            color: white;
            border-color: #008080;
        }

        .signup-form {
            display: none;
        }

        .signup-form.active {
            display: block;
        }

        .alert {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="container">
            <div class="signup-box">
                <div class="signup-logo">
                    <img src="../img/logo.png" alt="Echo Hotels">
                </div>

                <div class="user-type-selector text-center">
                    <button type="button" class="btn btn-outline-primary <?php echo $signup_type === 'customer' ? 'active' : ''; ?>" onclick="showForm('customer')">Customer</button>
                    <button type="button" class="btn btn-outline-primary <?php echo $signup_type === 'agent' ? 'active' : ''; ?>" onclick="showForm('agent')">Travel Agent</button>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?>" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Customer Signup Form -->
                <form method="POST" action="signup.php" class="signup-form <?php echo $signup_type === 'customer' ? 'active' : ''; ?>" id="customer-form">
                    <input type="hidden" name="user_type" value="customer">

                    <!-- Hidden fields for booking data -->
                    <?php if (isset($_POST['return_to']) && $_POST['return_to'] === 'booking'): ?>
                    <input type="hidden" name="return_to" value="booking">
                    <input type="hidden" name="hotel_id" value="<?php echo htmlspecialchars($_POST['hotel_id'] ?? ''); ?>">
                    <input type="hidden" name="room_type_id" value="<?php echo htmlspecialchars($_POST['room_type_id'] ?? ''); ?>">
                    <input type="hidden" name="check_in" value="<?php echo htmlspecialchars($_POST['check_in'] ?? ''); ?>">
                    <input type="hidden" name="check_out" value="<?php echo htmlspecialchars($_POST['check_out'] ?? ''); ?>">
                    <input type="hidden" name="guests" value="<?php echo htmlspecialchars($_POST['guests'] ?? ''); ?>">
                    <input type="hidden" name="booking_type" value="<?php echo htmlspecialchars($_POST['booking_type'] ?? 'guest'); ?>">
                    <input type="hidden" name="rate_type" value="<?php echo htmlspecialchars($_POST['rate_type'] ?? 'daily'); ?>">
                    <input type="hidden" name="company_name" value="<?php echo htmlspecialchars($_POST['company_name'] ?? ''); ?>">
                    <input type="hidden" name="rooms_count" value="<?php echo htmlspecialchars($_POST['rooms_count'] ?? 1); ?>">
                    <input type="hidden" name="nights" value="<?php echo htmlspecialchars($_POST['nights'] ?? 1); ?>">
                    <input type="hidden" name="discount_rate" value="<?php echo htmlspecialchars($_POST['discount_rate'] ?? 0); ?>">
                    <input type="hidden" name="billing_info" value="<?php echo htmlspecialchars($_POST['billing_info'] ?? ''); ?>">
                    <?php endif; ?>

                    <h3 class="text-center mb-4">Customer Registration</h3>

                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" name="first_name" class="form-control" placeholder="First Name" required>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="last_name" class="form-control" placeholder="Last Name" required>
                        </div>
                    </div>

                    <input type="email" name="email" class="form-control" placeholder="Email" required>
                    <input type="tel" name="phone" class="form-control" placeholder="Phone Number" required>
                    <input type="password" name="password" class="form-control" placeholder="Password" required>
                    <input type="password" name="confirm_password" class="form-control" placeholder="Confirm Password" required>

                    <button type="submit" class="signup-btn">Register as Customer</button>
                </form>

                <!-- Travel Agent Signup Form -->
                <form method="POST" action="signup.php" class="signup-form <?php echo $signup_type === 'agent' ? 'active' : ''; ?>" id="agent-form">
                    <input type="hidden" name="user_type" value="agent">

                    <!-- Hidden fields for booking data -->
                    <?php if (isset($_POST['return_to']) && $_POST['return_to'] === 'booking'): ?>
                    <input type="hidden" name="return_to" value="booking">
                    <input type="hidden" name="hotel_id" value="<?php echo htmlspecialchars($_POST['hotel_id'] ?? ''); ?>">
                    <input type="hidden" name="room_type_id" value="<?php echo htmlspecialchars($_POST['room_type_id'] ?? ''); ?>">
                    <input type="hidden" name="check_in" value="<?php echo htmlspecialchars($_POST['check_in'] ?? ''); ?>">
                    <input type="hidden" name="check_out" value="<?php echo htmlspecialchars($_POST['check_out'] ?? ''); ?>">
                    <input type="hidden" name="guests" value="<?php echo htmlspecialchars($_POST['guests'] ?? ''); ?>">
                    <input type="hidden" name="booking_type" value="<?php echo htmlspecialchars($_POST['booking_type'] ?? 'agent'); ?>">
                    <input type="hidden" name="rate_type" value="<?php echo htmlspecialchars($_POST['rate_type'] ?? 'daily'); ?>">
                    <input type="hidden" name="company_name" value="<?php echo htmlspecialchars($_POST['company_name'] ?? ''); ?>">
                    <input type="hidden" name="rooms_count" value="<?php echo htmlspecialchars($_POST['rooms_count'] ?? 3); ?>">
                    <input type="hidden" name="nights" value="<?php echo htmlspecialchars($_POST['nights'] ?? 1); ?>">
                    <input type="hidden" name="discount_rate" value="<?php echo htmlspecialchars($_POST['discount_rate'] ?? 10); ?>">
                    <input type="hidden" name="billing_info" value="<?php echo htmlspecialchars($_POST['billing_info'] ?? ''); ?>">
                    <?php endif; ?>

                    <h3 class="text-center mb-4">Travel Agent Registration</h3>

                    <input type="text" name="company_name" class="form-control" placeholder="Company Name" required>
                    <input type="email" name="contact_email" class="form-control" placeholder="Contact Email" required>
                    <input type="tel" name="contact_phone" class="form-control" placeholder="Contact Phone" required>
                    <textarea name="address" class="form-control" placeholder="Company Address" rows="3" required></textarea>
                    <textarea name="contract_details" class="form-control" placeholder="Contract Details" rows="3" required></textarea>
                    <input type="password" name="password" class="form-control" placeholder="Password" required>
                    <input type="password" name="confirm_password" class="form-control" placeholder="Confirm Password" required>

                    <button type="submit" class="signup-btn">Register as Travel Agent</button>
                </form>

                <div class="text-center mt-3">
                    <p>Already have an account? <a href="login.php" style="color: #008080; text-decoration: none;" onmouseover="this.style.color='#000'" onmouseout="this.style.color='#008080'">Login here</a></p>
                    <p><a href="../index.php" style="color: #008080; text-decoration: none;" onmouseover="this.style.color='#000'" onmouseout="this.style.color='#008080'">Back to Home</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showForm(type) {
            // Hide all forms
            document.querySelectorAll('.signup-form').forEach(form => {
                form.classList.remove('active');
            });

            // Show selected form
            document.getElementById(type + '-form').classList.add('active');

            // Update button states
            document.querySelectorAll('.user-type-selector .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
