<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo '<div class="alert alert-danger">Unauthorized access</div>';
    exit();
}

// Check if room_id is provided
if (!isset($_POST['room_id']) || empty($_POST['room_id'])) {
    echo '<div class="alert alert-danger">Room ID is required</div>';
    exit();
}

$room_id = intval($_POST['room_id']);

try {
    // Get room details with hotel and room type information
    $room_query = "
        SELECT r.*, h.Name as HotelName, h.Location as HotelLocation,
               rt.TypeName, rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate, rt.Description as RoomTypeDescription
        FROM Rooms r
        JOIN Hotels h ON r.HotelID = h.HotelID
        JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
        WHERE r.RoomID = ?
    ";

    $stmt = $conn->prepare($room_query);
    $stmt->bind_param("i", $room_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo '<div class="alert alert-danger">Room not found</div>';
        exit();
    }

    $room = $result->fetch_assoc();

    // Get current reservation if room is occupied
    $current_reservation = null;
    if ($room['Status'] === 'Occupied') {
        $reservation_query = "
            SELECT r.*, c.FirstName, c.LastName, c.Email, c.Phone
            FROM CheckIns ci
            JOIN Reservations r ON ci.ReservationID = r.ReservationID
            JOIN Customers c ON r.CustomerID = c.CustomerID
            WHERE ci.RoomID = ? AND r.Status = 'CheckedIn'
            AND ci.ActualCheckOutDateTime IS NULL
            ORDER BY ci.CheckInDateTime DESC
            LIMIT 1
        ";

        $res_stmt = $conn->prepare($reservation_query);
        $res_stmt->bind_param("i", $room_id);
        $res_stmt->execute();
        $res_result = $res_stmt->get_result();

        if ($res_result->num_rows > 0) {
            $current_reservation = $res_result->fetch_assoc();
        }
    }

    // Get upcoming reservations for this room type and hotel
    $upcoming_query = "
        SELECT r.*, c.FirstName, c.LastName, c.Email
        FROM Reservations r
        JOIN Customers c ON r.CustomerID = c.CustomerID
        WHERE r.HotelID = ? AND r.RoomTypeID = ? AND r.Status = 'Confirmed'
        AND r.StartDate > CURDATE()
        ORDER BY r.StartDate ASC
        LIMIT 5
    ";

    $upcoming_stmt = $conn->prepare($upcoming_query);
    $upcoming_stmt->bind_param("ii", $room['HotelID'], $room['RoomTypeID']);
    $upcoming_stmt->execute();
    $upcoming_result = $upcoming_stmt->get_result();

} catch (Exception $e) {
    echo '<div class="alert alert-danger">Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit();
}
?>

<div class="room-details">
    <div class="row">
        <div class="col-md-6">
            <h5><i class="fa fa-bed"></i> Room Information</h5>
            <table class="table table-borderless">
                <tr>
                    <td><strong>Room Number:</strong></td>
                    <td><?php echo htmlspecialchars($room['RoomNumber']); ?></td>
                </tr>
                <tr>
                    <td><strong>Hotel:</strong></td>
                    <td><?php echo htmlspecialchars($room['HotelName']); ?></td>
                </tr>
                <tr>
                    <td><strong>Location:</strong></td>
                    <td><?php echo htmlspecialchars($room['HotelLocation']); ?></td>
                </tr>
                <tr>
                    <td><strong>Room Type:</strong></td>
                    <td><?php echo htmlspecialchars($room['TypeName']); ?></td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                        <span class="badge badge-<?php echo $room['Status'] === 'Available' ? 'success' : ($room['Status'] === 'Occupied' ? 'warning' : 'danger'); ?>">
                            <?php echo htmlspecialchars($room['Status']); ?>
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        <div class="col-md-6">
            <h5><i class="fa fa-money"></i> Pricing Information</h5>
            <table class="table table-borderless">
                <tr>
                    <td><strong>Daily Rate:</strong></td>
                    <td><?php echo format_currency($room['DailyRate']); ?></td>
                </tr>
                <tr>
                    <td><strong>Weekly Rate:</strong></td>
                    <td><?php echo format_currency($room['WeeklyRate']); ?></td>
                </tr>
                <tr>
                    <td><strong>Monthly Rate:</strong></td>
                    <td><?php echo format_currency($room['MonthlyRate']); ?></td>
                </tr>
            </table>

            <?php if (!empty($room['RoomTypeDescription'])): ?>
            <h6><i class="fa fa-info-circle"></i> Description</h6>
            <p class="text-muted"><?php echo htmlspecialchars($room['RoomTypeDescription']); ?></p>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($current_reservation): ?>
    <hr>
    <h5><i class="fa fa-user"></i> Current Guest</h5>
    <div class="alert alert-info">
        <div class="row">
            <div class="col-md-6">
                <strong>Guest:</strong> <?php echo htmlspecialchars($current_reservation['FirstName'] . ' ' . $current_reservation['LastName']); ?><br>
                <strong>Email:</strong> <?php echo htmlspecialchars($current_reservation['Email']); ?><br>
                <strong>Phone:</strong> <?php echo htmlspecialchars($current_reservation['Phone']); ?>
            </div>
            <div class="col-md-6">
                <strong>Check-in:</strong> <?php echo date('M d, Y', strtotime($current_reservation['StartDate'])); ?><br>
                <strong>Check-out:</strong> <?php echo date('M d, Y', strtotime($current_reservation['EndDate'])); ?><br>
                <strong>Reservation ID:</strong> <?php echo $current_reservation['ReservationID']; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($upcoming_result->num_rows > 0): ?>
    <hr>
    <h5><i class="fa fa-calendar"></i> Upcoming Reservations (<?php echo htmlspecialchars($room['TypeName']); ?> rooms)</h5>
    <div class="table-responsive">
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Guest</th>
                    <th>Check-in</th>
                    <th>Check-out</th>
                    <th>Reservation ID</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($upcoming = $upcoming_result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($upcoming['FirstName'] . ' ' . $upcoming['LastName']); ?></td>
                    <td><?php echo date('M d, Y', strtotime($upcoming['StartDate'])); ?></td>
                    <td><?php echo date('M d, Y', strtotime($upcoming['EndDate'])); ?></td>
                    <td><?php echo $upcoming['ReservationID']; ?></td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
    <?php else: ?>
    <hr>
    <div class="alert alert-success">
        <i class="fa fa-check-circle"></i> No upcoming reservations for <?php echo htmlspecialchars($room['TypeName']); ?> rooms in this hotel.
    </div>
    <?php endif; ?>
</div>

<style>
.room-details .table td {
    padding: 0.5rem 0.75rem;
}

.room-details .badge {
    font-size: 0.9rem;
}

.room-details h5 {
    color: #667eea;
    margin-bottom: 1rem;
}

.room-details h6 {
    color: #764ba2;
    margin-top: 1rem;
}
</style>
