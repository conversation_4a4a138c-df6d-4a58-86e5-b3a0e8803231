<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $billing_id = intval($_POST['billing_id']);
    
    // Validate input
    if (empty($billing_id)) {
        echo json_encode(['success' => false, 'message' => 'Billing ID is required']);
        exit();
    }
    
    try {
        // Check if billing record exists and is pending
        $check_query = "SELECT BillingID, PaymentStatus, TotalAmount FROM Billing WHERE BillingID = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $billing_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'Billing record not found']);
            exit();
        }
        
        $billing = $check_result->fetch_assoc();
        
        if ($billing['PaymentStatus'] === 'Paid') {
            echo json_encode(['success' => false, 'message' => 'This billing record is already marked as paid']);
            exit();
        }
        
        // Update payment status to paid
        $update_query = "UPDATE Billing SET PaymentStatus = 'Paid', UpdatedAt = NOW() WHERE BillingID = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("i", $billing_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => 'Billing record marked as paid successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'No changes made to billing record']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Error updating payment status: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
