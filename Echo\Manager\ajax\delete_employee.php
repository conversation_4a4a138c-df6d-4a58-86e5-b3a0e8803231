<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $employee_id = intval($_POST['employee_id']);
    
    // Validate input
    if (empty($employee_id)) {
        echo json_encode(['success' => false, 'message' => 'Employee ID is required']);
        exit();
    }
    
    // Prevent self-deletion
    if ($employee_id == $_SESSION['employee_id']) {
        echo json_encode(['success' => false, 'message' => 'You cannot delete your own account']);
        exit();
    }
    
    try {
        // Check if employee exists
        $check_query = "SELECT EmployeeID, FirstName, LastName FROM Employees WHERE EmployeeID = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $employee_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'Employee not found']);
            exit();
        }
        
        $employee = $check_result->fetch_assoc();
        
        // Delete employee
        $delete_query = "DELETE FROM Employees WHERE EmployeeID = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $employee_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => 'Employee deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Employee not found']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Error deleting employee: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
