<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Check if hotel_id is provided
if (!isset($_POST['hotel_id']) || !is_numeric($_POST['hotel_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid hotel ID']);
    exit();
}

$hotel_id = intval($_POST['hotel_id']);

try {
    // Get room types for the selected hotel
    $room_types_query = "
        SELECT rt.*, COUNT(r.RoomID) as AvailableRooms
        FROM RoomTypes rt
        LEFT JOIN Rooms r ON rt.RoomTypeID = r.RoomTypeID AND r.HotelID = ? AND r.Status = 'Available'
        GROUP BY rt.RoomTypeID
        ORDER BY rt.TypeName
    ";
    $stmt = $conn->prepare($room_types_query);
    $stmt->bind_param("i", $hotel_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $room_types = [];
    while ($row = $result->fetch_assoc()) {
        $room_types[] = [
            'RoomTypeID' => $row['RoomTypeID'],
            'TypeName' => $row['TypeName'],
            'DailyRate' => $row['DailyRate'],
            'WeeklyRate' => $row['WeeklyRate'],
            'MonthlyRate' => $row['MonthlyRate'],
            'AvailableRooms' => $row['AvailableRooms']
        ];
    }
    
    // Get hotel name
    $hotel_query = "SELECT Name FROM Hotels WHERE HotelID = ?";
    $stmt = $conn->prepare($hotel_query);
    $stmt->bind_param("i", $hotel_id);
    $stmt->execute();
    $hotel_result = $stmt->get_result();
    $hotel_name = $hotel_result->fetch_assoc()['Name'] ?? 'Unknown Hotel';
    
    echo json_encode([
        'success' => true,
        'room_types' => $room_types,
        'hotel_name' => $hotel_name
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>
