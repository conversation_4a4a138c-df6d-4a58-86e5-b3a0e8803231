<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';

// Handle reservation creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $booking_type = $_POST['booking_type']; // 'guest' or 'travel_agent'
    $selected_hotel_id = $_POST['hotel_id'] ?? $clerk_hotel_id; // Allow hotel selection
    $first_name = sanitize_input($_POST['first_name'] ?? '');
    $last_name = sanitize_input($_POST['last_name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $room_type_id = $_POST['room_type_id'];
    $checkin_date = $_POST['checkin_date'];
    $checkout_date = $_POST['checkout_date'];
    $number_of_guests = $_POST['number_of_guests'];
    $rate_type = $_POST['rate_type'];
    $credit_card_details = sanitize_input($_POST['credit_card_details'] ?? '');
    $special_requests = $_POST['special_requests'] ?? '';

    // Travel agent specific fields
    $company_id = $_POST['company_id'] ?? null;
    $rooms_count = $_POST['rooms_count'] ?? 1;
    $is_block_booking = isset($_POST['is_block_booking']) ? 1 : 0;
    $discount_percentage = $_POST['discount_percentage'] ?? 0;

    try {
        $conn->begin_transaction();

        $customer_id = null;
        $final_company_id = null;

        if ($booking_type === 'travel_agent') {
            // Travel agent booking
            $final_company_id = $company_id;

            // For travel agent bookings, customer details are optional
            if (!empty($email) && !empty($first_name)) {
                // Check if customer exists or create new one
                $customer_check_query = "SELECT CustomerID FROM Customers WHERE Email = ?";
                $stmt = $conn->prepare($customer_check_query);
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $existing_customer = $stmt->get_result()->fetch_assoc();

                if ($existing_customer) {
                    $customer_id = $existing_customer['CustomerID'];
                } else {
                    // Create new customer for travel agent booking
                    $password_hash = password_hash('temp123', PASSWORD_DEFAULT);
                    $create_customer_query = "
                        INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt)
                        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                    ";
                    $stmt = $conn->prepare($create_customer_query);
                    $stmt->bind_param("sssss", $first_name, $last_name, $email, $phone, $password_hash);
                    $stmt->execute();
                    $customer_id = $conn->insert_id;
                }
            }
        } else {
            // Individual guest booking
            if (!empty($email)) {
                $customer_check_query = "SELECT CustomerID FROM Customers WHERE Email = ?";
                $stmt = $conn->prepare($customer_check_query);
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $existing_customer = $stmt->get_result()->fetch_assoc();

                if ($existing_customer) {
                    $customer_id = $existing_customer['CustomerID'];

                    // Update customer details if provided
                    $update_customer_query = "
                        UPDATE Customers
                        SET FirstName = ?, LastName = ?, Phone = ?, UpdatedAt = NOW()
                        WHERE CustomerID = ?
                    ";
                    $stmt = $conn->prepare($update_customer_query);
                    $stmt->bind_param("sssi", $first_name, $last_name, $phone, $customer_id);
                    $stmt->execute();
                } else {
                    // Create new customer
                    $password_hash = password_hash('temp123', PASSWORD_DEFAULT);
                    $create_customer_query = "
                        INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt)
                        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                    ";
                    $stmt = $conn->prepare($create_customer_query);
                    $stmt->bind_param("sssss", $first_name, $last_name, $email, $phone, $password_hash);
                    $stmt->execute();
                    $customer_id = $conn->insert_id;
                }
            } else {
                // Create customer without email (walk-in style)
                $password_hash = password_hash('temp123', PASSWORD_DEFAULT);
                $create_customer_query = "
                    INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt)
                    VALUES (?, ?, NULL, ?, ?, NOW(), NOW())
                ";
                $stmt = $conn->prepare($create_customer_query);
                $stmt->bind_param("ssss", $first_name, $last_name, $phone, $password_hash);
                $stmt->execute();
                $customer_id = $conn->insert_id;
            }
        }

        // Check room availability
        $required_rooms = ($booking_type === 'travel_agent') ? $rooms_count : 1;
        $availability_query = "
            SELECT COUNT(*) as available_rooms
            FROM Rooms r
            WHERE r.HotelID = ? AND r.RoomTypeID = ? AND r.Status = 'Available'
            AND r.RoomID NOT IN (
                SELECT DISTINCT ci.RoomID
                FROM CheckIns ci
                JOIN Reservations res ON ci.ReservationID = res.ReservationID
                WHERE res.Status IN ('Confirmed', 'CheckedIn')
                AND (
                    (? < res.EndDate AND ? > res.StartDate)
                )
            )
        ";
        $stmt = $conn->prepare($availability_query);
        $stmt->bind_param("iiss", $selected_hotel_id, $room_type_id, $checkin_date, $checkout_date);
        $stmt->execute();
        $availability = $stmt->get_result()->fetch_assoc();

        if ($availability['available_rooms'] < $required_rooms) {
            throw new Exception("Only {$availability['available_rooms']} rooms available, but $required_rooms rooms requested");
        }

        // Calculate discounted rate for travel agents
        $discounted_rate = null;
        if ($booking_type === 'travel_agent' && $discount_percentage > 0) {
            // Get base rate
            $rate_query = "SELECT DailyRate, WeeklyRate, MonthlyRate FROM RoomTypes WHERE RoomTypeID = ?";
            $stmt = $conn->prepare($rate_query);
            $stmt->bind_param("i", $room_type_id);
            $stmt->execute();
            $rates = $stmt->get_result()->fetch_assoc();

            $base_rate = 0;
            if ($rate_type === 'Weekly') {
                $base_rate = $rates['WeeklyRate'];
            } elseif ($rate_type === 'Monthly') {
                $base_rate = $rates['MonthlyRate'];
            } else {
                $base_rate = $rates['DailyRate'];
            }

            $discounted_rate = $base_rate * (1 - $discount_percentage / 100) * $required_rooms;
        }

        // Create reservations (multiple for travel agents if block booking)
        $reservation_ids = [];

        for ($i = 0; $i < $required_rooms; $i++) {
            $reservation_query = "
                INSERT INTO Reservations (
                    CustomerID, HotelID, RoomTypeID, StartDate, EndDate,
                    NumberOfGuests, CreditCardDetails, Status, RateType,
                    CompanyID, IsBlockBooking, DiscountedRate,
                    SpecialRequests, CreatedAt, UpdatedAt
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'Confirmed', ?, ?, ?, ?, ?, NOW(), NOW())
            ";
            $stmt = $conn->prepare($reservation_query);
            $stmt->bind_param("iiisssssiids",
                $customer_id, $selected_hotel_id, $room_type_id, $checkin_date, $checkout_date,
                $number_of_guests, $credit_card_details, $rate_type,
                $final_company_id, $is_block_booking, $discounted_rate, $special_requests
            );
            $stmt->execute();
            $reservation_ids[] = $conn->insert_id;
        }

        $conn->commit();

        if ($booking_type === 'travel_agent' && count($reservation_ids) > 1) {
            $message = "Block booking created successfully! " . count($reservation_ids) . " reservations created. IDs: " . implode(', ', $reservation_ids);
        } else {
            $message = "Reservation created successfully! Reservation ID: " . $reservation_ids[0];
        }
        $message_type = 'success';

        // Clear form data
        $_POST = array();

    } catch (Exception $e) {
        $conn->rollback();
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get all hotels for selection
$hotels_query = "SELECT HotelID, Name, Location FROM Hotels ORDER BY Name";
$hotels_result = $conn->query($hotels_query);

// Get default hotel for display (clerk's assigned hotel)
$default_hotel_id = $_POST['hotel_id'] ?? $clerk_hotel_id;

// Get room types for the selected/default hotel
$room_types_query = "
    SELECT rt.*, COUNT(r.RoomID) as AvailableRooms
    FROM RoomTypes rt
    LEFT JOIN Rooms r ON rt.RoomTypeID = r.RoomTypeID AND r.HotelID = ? AND r.Status = 'Available'
    GROUP BY rt.RoomTypeID
    ORDER BY rt.TypeName
";
$stmt = $conn->prepare($room_types_query);
$stmt->bind_param("i", $default_hotel_id);
$stmt->execute();
$room_types_result = $stmt->get_result();

// Get travel companies
$companies_query = "
    SELECT CompanyID, CompanyName,
           COALESCE(DiscountRate, 0) as DiscountRate,
           COALESCE(ContactPerson, '') as ContactPerson
    FROM TravelCompanies
    ORDER BY CompanyName
";
$companies_result = $conn->query($companies_query);

// Get selected hotel name for display
$hotel_query = "SELECT Name FROM Hotels WHERE HotelID = ?";
$stmt = $conn->prepare($hotel_query);
$stmt->bind_param("i", $default_hotel_id);
$stmt->execute();
$hotel_name = $stmt->get_result()->fetch_assoc()['Name'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Make Reservation - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Make Reservation - All Hotels</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Create New Reservation</h3>
                        <p class="mb-0 text-muted">Create reservations for individual guests or travel agents</p>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <!-- Booking Type Selection -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h5 class="text-primary">Booking Type</h5>
                                    <div class="form-group">
                                        <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                            <label class="btn btn-outline-primary active">
                                                <input type="radio" name="booking_type" value="guest" checked onchange="toggleBookingType()">
                                                <i class="fa fa-user"></i> Individual Guest
                                            </label>
                                            <label class="btn btn-outline-success">
                                                <input type="radio" name="booking_type" value="travel_agent" onchange="toggleBookingType()">
                                                <i class="fa fa-building"></i> Travel Agent/Company
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Hotel Selection -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h5 class="text-primary">Hotel Selection</h5>
                                    <div class="form-group">
                                        <label>Select Hotel *</label>
                                        <select name="hotel_id" id="hotel_id" class="form-control" required onchange="updateHotelRoomTypes()">
                                            <?php while ($hotel = $hotels_result->fetch_assoc()): ?>
                                                <option value="<?php echo $hotel['HotelID']; ?>"
                                                        <?php echo ($default_hotel_id == $hotel['HotelID']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($hotel['Name']); ?> - <?php echo htmlspecialchars($hotel['Location']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                        <small class="text-muted">You can make reservations for any hotel location</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Customer Information -->
                                <div class="col-md-6">
                                    <h5 class="text-primary">Customer Information</h5>
                                    <div class="form-group">
                                        <label>First Name *</label>
                                        <input type="text" name="first_name" class="form-control" required
                                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>">
                                    </div>
                                    <div class="form-group">
                                        <label>Last Name *</label>
                                        <input type="text" name="last_name" class="form-control" required
                                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>">
                                    </div>
                                    <div class="form-group">
                                        <label>Email Address</label>
                                        <input type="email" name="email" class="form-control"
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                               placeholder="Optional - for future bookings">
                                        <small class="text-muted">Leave empty for walk-in guests</small>
                                    </div>
                                    <div class="form-group">
                                        <label>Phone Number</label>
                                        <input type="tel" name="phone" class="form-control"
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                    </div>
                                    <div class="form-group">
                                        <label>Number of Occupants *</label>
                                        <input type="number" name="number_of_guests" class="form-control"
                                               min="1" max="10" value="<?php echo $_POST['number_of_guests'] ?? 1; ?>" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Travel Agent Specific Fields -->
                            <div id="travel_agent_fields" class="row" style="display: none;">
                                <div class="col-md-12">
                                    <h5 class="text-success">Travel Agent Information</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Travel Company *</label>
                                                <select name="company_id" class="form-control" onchange="updateCompanyDiscount()">
                                                    <option value="">Select travel company</option>
                                                    <?php while ($company = $companies_result->fetch_assoc()): ?>
                                                        <option value="<?php echo $company['CompanyID']; ?>"
                                                                data-discount="<?php echo $company['DiscountRate']; ?>">
                                                            <?php echo htmlspecialchars($company['CompanyName']); ?>
                                                            <?php if ($company['DiscountRate'] > 0): ?>
                                                                (<?php echo $company['DiscountRate']; ?>% discount)
                                                            <?php endif; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Number of Rooms *</label>
                                                <input type="number" name="rooms_count" class="form-control"
                                                       min="1" max="50" value="1" onchange="updatePricing()">
                                                <small class="text-muted">Minimum 3 rooms for block booking discount</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Discount Percentage</label>
                                                <input type="number" name="discount_percentage" id="discount_percentage"
                                                       class="form-control" min="0" max="50" step="0.1" value="0"
                                                       onchange="updatePricing()" readonly>
                                                <small class="text-muted">Automatically set based on company</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <div class="form-check mt-4">
                                                    <input type="checkbox" name="is_block_booking" class="form-check-input" id="is_block_booking">
                                                    <label class="form-check-label" for="is_block_booking">
                                                        Block Booking (3+ rooms, 1+ nights)
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Continue with booking information -->

                                <!-- Booking Information -->
                                <div class="col-md-6">
                                    <h5 class="text-primary">Booking Information</h5>
                                    <div class="form-group">
                                        <label>Room Type *</label>
                                        <select name="room_type_id" id="room_type_id" class="form-control" required onchange="updatePricing()">
                                            <option value="">Select room type</option>
                                            <?php while ($room_type = $room_types_result->fetch_assoc()): ?>
                                                <option value="<?php echo $room_type['RoomTypeID']; ?>"
                                                        data-daily="<?php echo $room_type['DailyRate']; ?>"
                                                        data-weekly="<?php echo $room_type['WeeklyRate']; ?>"
                                                        data-monthly="<?php echo $room_type['MonthlyRate']; ?>"
                                                        <?php echo ($_POST['room_type_id'] ?? '') == $room_type['RoomTypeID'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($room_type['TypeName']); ?>
                                                    (Rs. <?php echo number_format($room_type['DailyRate']); ?>/night)
                                                    - <?php echo $room_type['AvailableRooms']; ?> available
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Arrival Date *</label>
                                                <input type="date" name="checkin_date" class="form-control"
                                                       value="<?php echo $_POST['checkin_date'] ?? date('Y-m-d'); ?>"
                                                       min="<?php echo date('Y-m-d'); ?>" required onchange="updatePricing()">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Departure Date *</label>
                                                <input type="date" name="checkout_date" class="form-control"
                                                       value="<?php echo $_POST['checkout_date'] ?? date('Y-m-d', strtotime('+1 day')); ?>"
                                                       min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>" required onchange="updatePricing()">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>Rate Type *</label>
                                        <select name="rate_type" id="rate_type" class="form-control" required onchange="updatePricing()">
                                            <option value="Daily" <?php echo ($_POST['rate_type'] ?? '') === 'Daily' ? 'selected' : ''; ?>>Daily Rate</option>
                                            <option value="Weekly" <?php echo ($_POST['rate_type'] ?? '') === 'Weekly' ? 'selected' : ''; ?>>Weekly Rate (7+ nights)</option>
                                            <option value="Monthly" <?php echo ($_POST['rate_type'] ?? '') === 'Monthly' ? 'selected' : ''; ?>>Monthly Rate (30+ nights)</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Estimated Total</label>
                                        <input type="text" id="estimated_total" class="form-control" readonly
                                               style="font-weight: bold; font-size: 1.1em; background-color: #f8f9fa;">
                                    </div>
                                </div>
                            </div>

                            <!-- Credit Card Information -->
                            <div class="row">
                                <div class="col-md-12">
                                    <h5 class="text-primary">Payment Information</h5>
                                    <div class="alert alert-info">
                                        <i class="fa fa-info-circle"></i>
                                        <strong>Important:</strong> Reservations without credit card details will be automatically cancelled at 7:00 PM daily.
                                    </div>
                                    <div class="credit-card-form">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label for="card_number">
                                                        <i class="fa fa-credit-card"></i> Card Number
                                                    </label>
                                                    <input type="text" class="form-control" id="card_number" name="card_number"
                                                           placeholder="1234 5678 9012 3456" maxlength="19"
                                                           pattern="[0-9\s]{13,19}" autocomplete="cc-number">
                                                    <div class="card-type-icons">
                                                        <i class="fa fa-cc-visa" title="Visa"></i>
                                                        <i class="fa fa-cc-mastercard" title="Mastercard"></i>
                                                        <i class="fa fa-cc-amex" title="American Express"></i>
                                                        <i class="fa fa-cc-discover" title="Discover"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="expiry_date">
                                                        <i class="fa fa-calendar"></i> Expiry Date
                                                    </label>
                                                    <input type="text" class="form-control" id="expiry_date" name="expiry_date"
                                                           placeholder="MM/YY" maxlength="5" pattern="[0-9]{2}/[0-9]{2}"
                                                           autocomplete="cc-exp">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="cvv">
                                                        <i class="fa fa-lock"></i> CVV
                                                    </label>
                                                    <input type="text" class="form-control" id="cvv" name="cvv"
                                                           placeholder="123" maxlength="4" pattern="[0-9]{3,4}"
                                                           autocomplete="cc-csc">
                                                    <small class="form-text text-muted">3-4 digits on back of card</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="card_name">
                                                <i class="fa fa-user"></i> Name on Card
                                            </label>
                                            <input type="text" class="form-control" id="card_name" name="card_name"
                                                   placeholder="John Doe" autocomplete="cc-name">
                                        </div>

                                        <div class="security-notice">
                                            <i class="fa fa-shield"></i>
                                            <strong>Secure:</strong> Your payment information is encrypted and protected with SSL security.
                                        </div>

                                        <small class="text-muted">
                                            <i class="fa fa-info-circle"></i> Leave empty for cash payment at check-in
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Special Requests -->
                            <div class="form-group">
                                <label>Special Requests/Notes</label>
                                <textarea name="special_requests" class="form-control" rows="3"
                                          placeholder="Any special requests, dietary requirements, accessibility needs, etc."><?php echo htmlspecialchars($_POST['special_requests'] ?? ''); ?></textarea>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fa fa-calendar-plus"></i> Create Reservation
                                </button>
                                <a href="dashboard.php" class="btn btn-secondary btn-lg ml-2">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        function updateHotelRoomTypes() {
            const hotelId = document.getElementById('hotel_id').value;
            const roomTypeSelect = document.getElementById('room_type_id');

            if (!hotelId) {
                return;
            }

            // Show loading state
            roomTypeSelect.innerHTML = '<option value="">Loading room types...</option>';
            roomTypeSelect.disabled = true;

            // Make AJAX request to get room types for selected hotel
            $.ajax({
                url: 'ajax/get_room_types.php',
                method: 'POST',
                data: { hotel_id: hotelId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Clear existing options
                        roomTypeSelect.innerHTML = '<option value="">Select room type</option>';

                        // Add new room type options
                        response.room_types.forEach(function(roomType) {
                            const option = document.createElement('option');
                            option.value = roomType.RoomTypeID;
                            option.setAttribute('data-daily', roomType.DailyRate);
                            option.setAttribute('data-weekly', roomType.WeeklyRate);
                            option.setAttribute('data-monthly', roomType.MonthlyRate);
                            option.textContent = roomType.TypeName +
                                ' (Rs. ' + Number(roomType.DailyRate).toLocaleString() + '/night)' +
                                ' - ' + roomType.AvailableRooms + ' available';
                            roomTypeSelect.appendChild(option);
                        });

                        roomTypeSelect.disabled = false;
                        updatePricing();
                    } else {
                        roomTypeSelect.innerHTML = '<option value="">Error loading room types</option>';
                        console.error('Error:', response.error);
                    }
                },
                error: function(xhr, status, error) {
                    roomTypeSelect.innerHTML = '<option value="">Error loading room types</option>';
                    roomTypeSelect.disabled = false;
                    console.error('AJAX Error:', error);
                }
            });
        }

        function toggleBookingType() {
            const bookingType = document.querySelector('input[name="booking_type"]:checked').value;
            const travelAgentFields = document.getElementById('travel_agent_fields');
            const customerFields = document.querySelector('.col-md-6 h5').parentElement;

            if (bookingType === 'travel_agent') {
                travelAgentFields.style.display = 'block';
                // Make customer fields optional for travel agents
                document.querySelector('input[name="first_name"]').required = false;
                document.querySelector('input[name="last_name"]').required = false;
                document.querySelector('select[name="company_id"]').required = true;
            } else {
                travelAgentFields.style.display = 'none';
                // Make customer fields required for individual guests
                document.querySelector('input[name="first_name"]').required = true;
                document.querySelector('input[name="last_name"]').required = true;
                document.querySelector('select[name="company_id"]').required = false;
            }
            updatePricing();
        }

        function updateCompanyDiscount() {
            const companySelect = document.querySelector('select[name="company_id"]');
            const discountField = document.getElementById('discount_percentage');
            const blockBookingCheckbox = document.getElementById('is_block_booking');

            if (companySelect.value) {
                const selectedOption = companySelect.options[companySelect.selectedIndex];
                const discount = parseFloat(selectedOption.dataset.discount) || 0;
                discountField.value = discount;

                // Auto-check block booking if 3+ rooms
                const roomsCount = parseInt(document.querySelector('input[name="rooms_count"]').value) || 1;
                if (roomsCount >= 3) {
                    blockBookingCheckbox.checked = true;
                }
            } else {
                discountField.value = 0;
                blockBookingCheckbox.checked = false;
            }
            updatePricing();
        }

        function updatePricing() {
            const roomTypeSelect = document.getElementById('room_type_id');
            const checkinDate = document.querySelector('input[name="checkin_date"]').value;
            const checkoutDate = document.querySelector('input[name="checkout_date"]').value;
            const rateType = document.getElementById('rate_type').value;
            const bookingType = document.querySelector('input[name="booking_type"]:checked').value;

            if (!roomTypeSelect.value || !checkinDate || !checkoutDate) {
                document.getElementById('estimated_total').value = '';
                return;
            }

            const selectedOption = roomTypeSelect.options[roomTypeSelect.selectedIndex];
            const dailyRate = parseFloat(selectedOption.dataset.daily);
            const weeklyRate = parseFloat(selectedOption.dataset.weekly);
            const monthlyRate = parseFloat(selectedOption.dataset.monthly);

            const checkin = new Date(checkinDate);
            const checkout = new Date(checkoutDate);
            const nights = Math.ceil((checkout - checkin) / (1000 * 60 * 60 * 24));

            if (nights <= 0) {
                document.getElementById('estimated_total').value = 'Invalid dates';
                return;
            }

            let baseRate = 0;
            if (rateType === 'Weekly') {
                const weeks = Math.ceil(nights / 7);
                baseRate = weeks * weeklyRate;
            } else if (rateType === 'Monthly') {
                const months = Math.ceil(nights / 30);
                baseRate = months * monthlyRate;
            } else {
                baseRate = nights * dailyRate;
            }

            let total = baseRate;
            let roomsCount = 1;
            let discountText = '';

            if (bookingType === 'travel_agent') {
                roomsCount = parseInt(document.querySelector('input[name="rooms_count"]').value) || 1;
                const discount = parseFloat(document.getElementById('discount_percentage').value) || 0;

                total = baseRate * roomsCount;

                if (discount > 0) {
                    const discountAmount = total * (discount / 100);
                    total = total - discountAmount;
                    discountText = ` (${discount}% discount applied)`;
                }
            }

            document.getElementById('estimated_total').value =
                'Rs. ' + total.toLocaleString() +
                ' (' + nights + ' nights' +
                (roomsCount > 1 ? ', ' + roomsCount + ' rooms' : '') +
                discountText + ')';
        }

        // Credit Card Functionality
        function formatCardNumber(input) {
            let value = input.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;

            if (formattedValue.length > 19) {
                formattedValue = formattedValue.substring(0, 19);
            }

            input.value = formattedValue;
            detectCardType(value);
        }

        function formatExpiryDate(input) {
            let value = input.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            input.value = value;
        }

        function formatCVV(input) {
            let value = input.value.replace(/\D/g, '');
            input.value = value;
        }

        function formatCardName(input) {
            let value = input.value.replace(/[^a-zA-Z\s]/g, '');
            input.value = value.toUpperCase();
        }

        function detectCardType(number) {
            const icons = document.querySelectorAll('.card-type-icons i');
            icons.forEach(icon => icon.classList.remove('active'));

            if (number.match(/^4/)) {
                document.querySelector('.fa-cc-visa').classList.add('active');
            } else if (number.match(/^5[1-5]/)) {
                document.querySelector('.fa-cc-mastercard').classList.add('active');
            } else if (number.match(/^3[47]/)) {
                document.querySelector('.fa-cc-amex').classList.add('active');
            } else if (number.match(/^6/)) {
                document.querySelector('.fa-cc-discover').classList.add('active');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleBookingType();
            updatePricing();

            // Credit card event listeners
            const cardNumber = document.getElementById('card_number');
            const expiryDate = document.getElementById('expiry_date');
            const cvv = document.getElementById('cvv');
            const cardName = document.getElementById('card_name');

            if (cardNumber) {
                cardNumber.addEventListener('input', function() {
                    formatCardNumber(this);
                });
            }

            if (expiryDate) {
                expiryDate.addEventListener('input', function() {
                    formatExpiryDate(this);
                });
            }

            if (cvv) {
                cvv.addEventListener('input', function() {
                    formatCVV(this);
                });
            }

            if (cardName) {
                cardName.addEventListener('input', function() {
                    formatCardName(this);
                });
            }
        });
    </script>
</body>
</html>
