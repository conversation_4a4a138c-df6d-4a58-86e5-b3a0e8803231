<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $company_id = intval($_POST['company_id']);
    $company_name = trim($_POST['company_name']);
    $contact_email = trim($_POST['contact_email']);
    $contact_phone = trim($_POST['contact_phone']);
    $address = trim($_POST['address']);
    
    // Validate input
    if (empty($company_id) || empty($company_name) || empty($contact_email) || empty($contact_phone)) {
        echo json_encode(['success' => false, 'message' => 'Company ID, name, contact email, and contact phone are required']);
        exit();
    }
    
    // Validate email format
    if (!filter_var($contact_email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit();
    }
    
    try {
        // Check if company name already exists for another company
        $check_query = "SELECT CompanyID FROM TravelCompanies WHERE CompanyName = ? AND CompanyID != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $company_name, $company_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Company name already exists for another company']);
            exit();
        }
        
        // Check if contact email already exists for another company
        $check_email_query = "SELECT CompanyID FROM TravelCompanies WHERE ContactEmail = ? AND CompanyID != ?";
        $check_email_stmt = $conn->prepare($check_email_query);
        $check_email_stmt->bind_param("si", $contact_email, $company_id);
        $check_email_stmt->execute();
        $check_email_result = $check_email_stmt->get_result();
        
        if ($check_email_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Contact email already exists for another company']);
            exit();
        }
        
        // Update company
        $update_query = "UPDATE TravelCompanies SET CompanyName = ?, ContactEmail = ?, ContactPhone = ?, Address = ?, UpdatedAt = NOW() WHERE CompanyID = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("ssssi", $company_name, $contact_email, $contact_phone, $address, $company_id);
        
        if ($stmt->execute()) {
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => 'Travel company updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Company not found or no changes made']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Error updating company: ' . $conn->error]);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
