<?php
/**
 * Auto-Cancellation Status API
 * 
 * Returns JSON status of the auto-cancellation system
 * Can be used for monitoring, dashboards, or external integrations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once(__DIR__ . '/../config/db.php');

// Set timezone
date_default_timezone_set('Asia/Colombo');

try {
    $current_time = date('Y-m-d H:i:s');
    $current_hour = (int)date('H');
    $today = date('Y-m-d');
    
    // Get reservations without credit card details
    $without_cc_query = "
        SELECT COUNT(*) as count 
        FROM Reservations r 
        WHERE r.Status = 'Confirmed' 
        AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '') 
        AND r.CustomerID IS NOT NULL 
        AND r.CompanyID IS NULL
    ";
    $without_cc_result = $conn->query($without_cc_query);
    $without_cc_count = $without_cc_result->fetch_assoc()['count'];
    
    // Get reservations eligible for cancellation
    $eligible_query = "
        SELECT COUNT(*) as count 
        FROM Reservations r 
        WHERE r.Status = 'Confirmed' 
        AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '') 
        AND r.CustomerID IS NOT NULL 
        AND r.CompanyID IS NULL
        AND (
            (DATE(r.CreatedAt) = CURDATE() AND HOUR(NOW()) >= 19) 
            OR DATE(r.CreatedAt) < CURDATE()
        )
    ";
    $eligible_result = $conn->query($eligible_query);
    $eligible_count = $eligible_result->fetch_assoc()['count'];
    
    // Get today's cancellations
    $cancelled_today_query = "
        SELECT COUNT(*) as count 
        FROM Reservations r 
        WHERE r.Status = 'Cancelled' 
        AND DATE(r.UpdatedAt) = CURDATE()
        AND r.CancellationReason LIKE '%Auto-cancelled%'
    ";
    $cancelled_today_result = $conn->query($cancelled_today_query);
    $cancelled_today_count = $cancelled_today_result->fetch_assoc()['count'];
    
    // Get today's no-shows
    $no_show_today_query = "
        SELECT COUNT(*) as count 
        FROM Reservations r 
        WHERE r.Status = 'No-Show' 
        AND DATE(r.UpdatedAt) = CURDATE()
    ";
    $no_show_today_result = $conn->query($no_show_today_query);
    $no_show_today_count = $no_show_today_result->fetch_assoc()['count'];
    
    // Check if log file exists and get last entry
    $log_file = __DIR__ . '/../cron/cleanup_log.txt';
    $last_log_entry = null;
    $log_file_size = 0;
    
    if (file_exists($log_file)) {
        $log_file_size = filesize($log_file);
        $log_content = file_get_contents($log_file);
        $log_lines = array_filter(explode("\n", $log_content));
        if (!empty($log_lines)) {
            $last_log_entry = end($log_lines);
        }
    }
    
    // Calculate next run time
    $next_run_time = date('Y-m-d 19:00:00');
    if ($current_hour >= 19) {
        $next_run_time = date('Y-m-d 19:00:00', strtotime('+1 day'));
    }
    
    // Check Windows scheduled task status (if on Windows)
    $scheduled_task_status = 'unknown';
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        $task_name = 'Echo_Hotel_Daily_Cleanup';
        $command = "schtasks /query /tn \"$task_name\" 2>nul";
        $output = shell_exec($command);
        
        if ($output && strpos($output, $task_name) !== false) {
            $scheduled_task_status = 'configured';
        } else {
            $scheduled_task_status = 'not_configured';
        }
    } else {
        $scheduled_task_status = 'linux_cron';
    }
    
    // Prepare response
    $response = [
        'status' => 'success',
        'timestamp' => $current_time,
        'system' => [
            'current_time' => $current_time,
            'current_hour' => $current_hour,
            'is_active_time' => $current_hour >= 19,
            'next_run_time' => $next_run_time,
            'timezone' => 'Asia/Colombo',
            'scheduled_task_status' => $scheduled_task_status
        ],
        'statistics' => [
            'reservations_without_credit_card' => (int)$without_cc_count,
            'eligible_for_cancellation' => (int)$eligible_count,
            'cancelled_today' => (int)$cancelled_today_count,
            'no_shows_today' => (int)$no_show_today_count
        ],
        'alerts' => [],
        'log_info' => [
            'log_file_exists' => file_exists($log_file),
            'log_file_size' => $log_file_size,
            'last_log_entry' => $last_log_entry
        ]
    ];
    
    // Add alerts based on conditions
    if ($eligible_count > 0) {
        $response['alerts'][] = [
            'type' => 'warning',
            'message' => "$eligible_count reservation(s) are eligible for auto-cancellation",
            'action_required' => $current_hour >= 19
        ];
    }
    
    if ($scheduled_task_status === 'not_configured') {
        $response['alerts'][] = [
            'type' => 'error',
            'message' => 'Windows scheduled task is not configured',
            'action_required' => true
        ];
    }
    
    if (!file_exists($log_file)) {
        $response['alerts'][] = [
            'type' => 'info',
            'message' => 'Log file does not exist yet - cleanup script has not run',
            'action_required' => false
        ];
    }
    
    // Add success message if no issues
    if (empty($response['alerts']) && $without_cc_count === 0) {
        $response['alerts'][] = [
            'type' => 'success',
            'message' => 'All systems operational - no reservations require cancellation',
            'action_required' => false
        ];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to retrieve auto-cancellation status',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}

$conn->close();
?>
