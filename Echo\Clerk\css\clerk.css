/* Clerk Dashboard Styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.clerk-wrapper {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow: hidden;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    top: 0;
    left: 0;
}

.sidebar-header {
    padding: 15px 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: bold;
}

.sidebar-header p {
    margin: 3px 0 0 0;
    opacity: 0.8;
    font-size: 0.85rem;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 1;
    overflow-y: auto;
}

.sidebar-menu li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu li a {
    display: block;
    padding: 10px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.sidebar-menu li a:hover,
.sidebar-menu li.active a {
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 30px;
}

.sidebar-menu li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Removed old profile section styles - now using sidebar-footer */

.sidebar-footer {
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.1);
    margin-top: auto;
    flex-shrink: 0;
}

.sidebar-footer .user-info {
    color: #ffffff;
    font-size: 0.85rem;
    margin-bottom: 10px;
    text-align: center;
    font-weight: 600;
}

.sidebar-footer .user-info i {
    margin-right: 8px;
    color: #ffd700;
}

.sidebar-footer .btn {
    width: 100%;
    border-radius: 8px;
    font-weight: 600;
    padding: 8px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    text-align: center;
    font-size: 0.85rem;
}

.sidebar-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
}

/* Main Content */
.main-content {
    margin-left: 250px !important;
    flex: 1;
    padding: 0;
    width: calc(100% - 250px);
    min-height: 100vh;
}

.header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    margin: 0;
    color: #333;
    font-size: 1.8rem;
}

.user-info {
    color: #666;
    font-size: 0.9rem;
}

.content {
    padding: 30px;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.card-body {
    padding: 20px;
}

/* Stats Cards */
.stats-card {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    color: white;
    margin-bottom: 20px;
}

.stats-card h3 {
    font-size: 2rem;
    margin: 0;
    font-weight: bold;
}

.stats-card p {
    margin: 5px 0 0 0;
    opacity: 0.9;
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #333;
    background-color: #f8f9fa;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #dee2e6;
    padding: 0 20px;
    height: 60px;
    font-size: 1rem;
    line-height: 60px;
    box-sizing: border-box;
}

/* Input fields should have normal line-height for text input */
input.form-control {
    line-height: 1.5;
    padding: 18px 20px;
}

/* Textarea should have normal line-height */
textarea.form-control {
    line-height: 1.5;
    padding: 15px 20px;
    height: auto;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 5px 10px;
}

/* Booking Type Toggle */
.btn-group-toggle .btn {
    border-radius: 8px !important;
    margin: 0 5px;
    font-weight: 600;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.btn-group-toggle .btn input[type="radio"] {
    display: none;
}

.btn-group-toggle .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-group-toggle .btn.active,
.btn-group-toggle .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Travel Agent Fields */
#travel_agent_fields {
    background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid #28a745;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Form Styling */
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

.form-group label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 20px;
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #4e9a2a 0%, #95d3bc 100%);
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Input Group Enhancements */
.input-group .form-control {
    border-radius: 8px 0 0 8px;
}

.input-group-append .btn {
    border-radius: 0 8px 8px 0;
}

/* Pricing Display */
#estimated_total {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border: 2px solid #28a745 !important;
    font-weight: bold !important;
    font-size: 1.1em !important;
    color: #155724 !important;
}

/* Section Headers */
h5.text-primary {
    color: #667eea !important;
    font-weight: 700;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #667eea;
}

h5.text-success {
    color: #28a745 !important;
    font-weight: 700;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #28a745;
}

/* DROPDOWN TEXT VISIBILITY FIX */
/* Universal Select Styling for Maximum Visibility */
select.form-control,
select {
    background-color: #ffffff !important;
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 1rem !important;
    border: 2px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 0 20px !important;
    height: 60px !important;
    line-height: 60px !important;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 18px;
    padding-right: 45px;
    vertical-align: middle;
    box-sizing: border-box !important;
}

select.form-control:focus,
select:focus {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
    outline: none !important;
}

/* Select Options Styling */
select.form-control option,
select option {
    background-color: #ffffff !important;
    color: #333333 !important;
    font-weight: normal !important;
    padding: 12px !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    min-height: 40px !important;
}

select.form-control option:hover,
select.form-control option:focus,
select.form-control option:checked,
select option:hover,
select option:focus,
select option:checked {
    background-color: #667eea !important;
    color: #ffffff !important;
}

/* Specific Dropdown Fixes */
select[name="room_type_id"] {
    background-color: #ffffff !important;
    color: #333333 !important;
    font-weight: normal !important;
    height: 60px !important;
    padding: 0 20px !important;
    font-size: 1rem !important;
    line-height: 60px !important;
    border: 2px solid #dee2e6 !important;
}

select[name="company_id"] {
    background-color: #ffffff !important;
    color: #333333 !important;
    font-weight: normal !important;
    height: 60px !important;
    padding: 0 20px !important;
    font-size: 1rem !important;
    line-height: 60px !important;
    border: 2px solid #dee2e6 !important;
}

select[name="rate_type"] {
    background-color: #ffffff !important;
    color: #333333 !important;
    font-weight: normal !important;
    height: 60px !important;
    padding: 0 20px !important;
    font-size: 1rem !important;
    line-height: 60px !important;
    border: 2px solid #dee2e6 !important;
}

/* Force text visibility on all browsers */
select[name="room_type_id"] option,
select[name="company_id"] option,
select[name="rate_type"] option {
    background-color: #ffffff !important;
    color: #333333 !important;
    font-weight: normal !important;
    padding: 12px !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    min-height: 40px !important;
}

/* Credit Card Form Styling */
.credit-card-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0;
    border: 2px solid #dee2e6;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.credit-card-form .form-group {
    position: relative;
    margin-bottom: 20px;
}

.credit-card-form label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.credit-card-form label i {
    margin-right: 8px;
    color: #667eea;
}

.credit-card-form .form-control {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.credit-card-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: #ffffff;
}

.card-type-icons {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 8px;
}

.card-type-icons i {
    font-size: 1.5rem;
    color: #dee2e6;
    transition: all 0.3s ease;
}

.card-type-icons i.active {
    color: #667eea;
    transform: scale(1.1);
}

.security-notice {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 1px solid #c3e6c3;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    font-size: 0.9rem;
    color: #155724;
    display: flex;
    align-items: center;
}

.security-notice i {
    color: #28a745;
    margin-right: 10px;
    font-size: 1.1rem;
}

/* Card number formatting */
#card_number {
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    font-size: 1.1rem;
    padding-right: 120px; /* Space for card icons */
}

#expiry_date, #cvv {
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    text-align: center;
}

#card_name {
    text-transform: uppercase;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .card-type-icons {
        position: static;
        transform: none;
        justify-content: center;
        margin-top: 10px;
    }

    #card_number {
        padding-right: 15px;
    }
}

/* Ensure main content is always positioned correctly on desktop */
@media (min-width: 769px) {
    .main-content {
        margin-left: 250px !important;
        width: calc(100% - 250px) !important;
        position: relative !important;
        left: 0 !important;
    }

    .sidebar {
        transform: translateX(0) !important;
        position: fixed !important;
        left: 0 !important;
        top: 0 !important;
        z-index: 1000 !important;
    }
}

/* Force layout fix for all screen sizes */
.clerk-wrapper .main-content {
    margin-left: 250px !important;
    width: calc(100% - 250px) !important;
    box-sizing: border-box !important;
    background-color: #f8f9fa !important;
    min-height: 100vh !important;
}

    .header {
        padding: 15px 20px;
    }

    .content {
        padding: 20px;
    }

    .btn-group-toggle {
        flex-direction: column;
    }

    .btn-group-toggle .btn {
        margin: 5px 0;
        width: 100%;
    }

    #travel_agent_fields {
        margin: 10px 0;
        padding: 15px;
    }
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    margin-bottom: 20px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stats-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-card p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 500;
}

/* Table Enhancements */
.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 15px;
}

.table tbody td {
    padding: 12px 15px;
    border-top: 1px solid #f1f3f4;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badge Enhancements */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Modal Enhancements */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 12px 12px;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .stats-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table {
        font-size: 12px;
    }
}
