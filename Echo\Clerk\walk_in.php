<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';

// Handle walk-in guest registration and check-in
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $room_type_id = $_POST['room_type_id'];
    $room_id = $_POST['room_id'];
    $checkin_date = $_POST['checkin_date'];
    $checkout_date = $_POST['checkout_date'];
    $number_of_guests = $_POST['number_of_guests'];
    $rate_type = $_POST['rate_type'];
    $special_requests = $_POST['special_requests'] ?? '';

    try {
        $conn->begin_transaction();

        $customer_id = null;

        // Check if customer exists or create new one
        if (!empty($email)) {
            // Check for existing customer by email
            $customer_check_query = "SELECT CustomerID FROM Customers WHERE Email = ?";
            $stmt = $conn->prepare($customer_check_query);
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $existing_customer = $stmt->get_result()->fetch_assoc();

            if ($existing_customer) {
                $customer_id = $existing_customer['CustomerID'];

                // Update customer details if provided
                $update_customer_query = "
                    UPDATE Customers
                    SET FirstName = ?, LastName = ?, Phone = ?, UpdatedAt = NOW()
                    WHERE CustomerID = ?
                ";
                $stmt = $conn->prepare($update_customer_query);
                $stmt->bind_param("sssi", $first_name, $last_name, $phone, $customer_id);
                $stmt->execute();
            } else {
                // Create new customer with email
                $password_hash = password_hash('walkin123', PASSWORD_DEFAULT);
                $create_customer_query = "
                    INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ";
                $stmt = $conn->prepare($create_customer_query);
                $stmt->bind_param("sssss", $first_name, $last_name, $email, $phone, $password_hash);
                $stmt->execute();
                $customer_id = $conn->insert_id;
            }
        } else {
            // Create new customer without email (walk-in guest)
            // Generate a unique email to avoid NULL constraint issues
            $unique_email = 'walkin_' . uniqid() . '_' . time() . '@temp.local';
            $password_hash = password_hash('walkin123', PASSWORD_DEFAULT);
            $create_customer_query = "
                INSERT INTO Customers (FirstName, LastName, Email, Phone, PasswordHash, CreatedAt, UpdatedAt)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ";
            $stmt = $conn->prepare($create_customer_query);
            $stmt->bind_param("sssss", $first_name, $last_name, $unique_email, $phone, $password_hash);
            $stmt->execute();
            $customer_id = $conn->insert_id;
        }

        // Calculate pricing
        $checkin_dt = new DateTime($checkin_date);
        $checkout_dt = new DateTime($checkout_date);
        $nights = $checkin_dt->diff($checkout_dt)->days;
        if ($nights == 0) $nights = 1;

        // Get room type rates
        $rate_query = "SELECT DailyRate, WeeklyRate, MonthlyRate FROM RoomTypes WHERE RoomTypeID = ?";
        $stmt = $conn->prepare($rate_query);
        $stmt->bind_param("i", $room_type_id);
        $stmt->execute();
        $rates = $stmt->get_result()->fetch_assoc();

        $total_amount = 0;
        if ($rate_type === 'Weekly') {
            $weeks = ceil($nights / 7);
            $total_amount = $weeks * $rates['WeeklyRate'];
        } elseif ($rate_type === 'Monthly') {
            $months = ceil($nights / 30);
            $total_amount = $months * $rates['MonthlyRate'];
        } else {
            $total_amount = $nights * $rates['DailyRate'];
        }

        // Create reservation
        $reservation_query = "
            INSERT INTO Reservations (
                CustomerID, HotelID, RoomTypeID, StartDate, EndDate,
                NumberOfGuests, Status, RateType, SpecialRequests,
                CreatedAt, UpdatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, 'Confirmed', ?, ?, NOW(), NOW())
        ";
        $stmt = $conn->prepare($reservation_query);
        $stmt->bind_param("iiississ", $customer_id, $clerk_hotel_id, $room_type_id, $checkin_date, $checkout_date, $number_of_guests, $rate_type, $special_requests);
        $stmt->execute();
        $reservation_id = $conn->insert_id;

        // Create check-in record immediately
        $checkin_query = "
            INSERT INTO CheckIns (
                ReservationID, CustomerID, HotelID, RoomID,
                CheckInDateTime, ExpectedCheckOutDate, CreatedAt, UpdatedAt
            ) VALUES (?, ?, ?, ?, NOW(), ?, NOW(), NOW())
        ";
        $stmt = $conn->prepare($checkin_query);
        $stmt->bind_param("iiiis", $reservation_id, $customer_id, $clerk_hotel_id, $room_id, $checkout_date);
        $stmt->execute();
        $checkin_id = $conn->insert_id;

        // Update reservation status to checked in
        $update_reservation_query = "UPDATE Reservations SET Status = 'CheckedIn' WHERE ReservationID = ?";
        $stmt = $conn->prepare($update_reservation_query);
        $stmt->bind_param("i", $reservation_id);
        $stmt->execute();

        // Update room status
        $update_room_query = "UPDATE Rooms SET Status = 'Occupied' WHERE RoomID = ?";
        $stmt = $conn->prepare($update_room_query);
        $stmt->bind_param("i", $room_id);
        $stmt->execute();

        // Create billing record
        $billing_query = "
            INSERT INTO Billing (
                ReservationID, CheckInID, TotalAmount, PaymentMethod,
                PaymentStatus, BillingDateTime, CreatedAt, UpdatedAt
            ) VALUES (?, ?, ?, 'Cash', 'Pending', NOW(), NOW(), NOW())
        ";
        $stmt = $conn->prepare($billing_query);
        $stmt->bind_param("iid", $reservation_id, $checkin_id, $total_amount);
        $stmt->execute();

        // Get room number for display
        $room_query = "SELECT RoomNumber FROM Rooms WHERE RoomID = ?";
        $stmt = $conn->prepare($room_query);
        $stmt->bind_param("i", $room_id);
        $stmt->execute();
        $room_number = $stmt->get_result()->fetch_assoc()['RoomNumber'];

        $conn->commit();
        $message = "Walk-in guest successfully registered and checked in!<br>
                   • Customer ID: $customer_id<br>
                   • Reservation ID: $reservation_id<br>
                   • Check-in ID: $checkin_id<br>
                   • Guest: $first_name $last_name<br>
                   • Room: $room_number<br>
                   • Total Amount: Rs. " . number_format($total_amount);
        $message_type = 'success';

    } catch (Exception $e) {
        $conn->rollback();
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get available rooms
$available_rooms_query = "
    SELECT r.RoomID, r.RoomNumber, rt.RoomTypeID, rt.TypeName, rt.DailyRate, rt.WeeklyRate, rt.MonthlyRate
    FROM Rooms r
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.HotelID = ? AND r.Status = 'Available'
    ORDER BY rt.TypeName, r.RoomNumber
";
$stmt = $conn->prepare($available_rooms_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$available_rooms = $stmt->get_result();

// Group rooms by type
$rooms_by_type = [];
$room_types = [];
while ($room = $available_rooms->fetch_assoc()) {
    $rooms_by_type[$room['RoomTypeID']][] = $room;
    if (!isset($room_types[$room['RoomTypeID']])) {
        $room_types[$room['RoomTypeID']] = [
            'TypeName' => $room['TypeName'],
            'DailyRate' => $room['DailyRate'],
            'WeeklyRate' => $room['WeeklyRate'],
            'MonthlyRate' => $room['MonthlyRate']
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Walk-In Guest - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="header">
                <h1>Walk-In Guest Registration</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>

            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo $message; // Don't escape HTML for formatted messages ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fa fa-user-plus"></i> Register Walk-In Guest</h3>
                        <p class="mb-0 text-muted">
                            <small>
                                <i class="fa fa-info-circle"></i>
                                This will create a customer record, reservation, and immediately check-in the guest.
                            </small>
                        </p>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Guest Information</h5>
                                    <div class="form-group">
                                        <label>First Name *</label>
                                        <input type="text" name="first_name" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Last Name *</label>
                                        <input type="text" name="last_name" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Email</label>
                                        <input type="email" name="email" class="form-control"
                                               placeholder="Optional - for future bookings">
                                    </div>
                                    <div class="form-group">
                                        <label>Phone</label>
                                        <input type="tel" name="phone" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label>Number of Guests *</label>
                                        <input type="number" name="number_of_guests" class="form-control"
                                               min="1" max="10" value="1" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5>Booking Information</h5>
                                    <div class="form-group">
                                        <label>Room Type *</label>
                                        <select name="room_type_id" id="room_type_id" class="form-control" required onchange="updateRoomOptions()">
                                            <option value="">Select room type</option>
                                            <?php foreach ($room_types as $type_id => $type): ?>
                                                <option value="<?php echo $type_id; ?>"
                                                        data-daily="<?php echo $type['DailyRate']; ?>"
                                                        data-weekly="<?php echo $type['WeeklyRate']; ?>"
                                                        data-monthly="<?php echo $type['MonthlyRate']; ?>">
                                                    <?php echo htmlspecialchars($type['TypeName']); ?>
                                                    (Rs. <?php echo number_format($type['DailyRate']); ?>/night)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Room Number *</label>
                                        <select name="room_id" id="room_id" class="form-control" required>
                                            <option value="">Select room type first</option>
                                        </select>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Check-in Date *</label>
                                                <input type="date" name="checkin_date" class="form-control"
                                                       value="<?php echo date('Y-m-d'); ?>" required onchange="calculateTotal()">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Check-out Date *</label>
                                                <input type="date" name="checkout_date" class="form-control"
                                                       value="<?php echo date('Y-m-d', strtotime('+1 day')); ?>" required onchange="calculateTotal()">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label>Rate Type *</label>
                                        <select name="rate_type" id="rate_type" class="form-control" required onchange="calculateTotal()">
                                            <option value="Daily">Daily Rate</option>
                                            <option value="Weekly">Weekly Rate</option>
                                            <option value="Monthly">Monthly Rate</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Total Amount</label>
                                        <input type="text" id="total_amount" class="form-control" readonly
                                               style="font-weight: bold; font-size: 1.1em;">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>Special Requests/Notes</label>
                                <textarea name="special_requests" class="form-control" rows="3"
                                          placeholder="Any special requests or notes..."></textarea>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fa fa-user-plus"></i> Register & Check-In Guest
                                </button>
                                <a href="dashboard.php" class="btn btn-secondary btn-lg ml-2">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        const roomsByType = <?php echo json_encode($rooms_by_type); ?>;
        const roomTypes = <?php echo json_encode($room_types); ?>;

        function updateRoomOptions() {
            const roomTypeId = document.getElementById('room_type_id').value;
            const roomSelect = document.getElementById('room_id');

            roomSelect.innerHTML = '<option value="">Select a room</option>';

            if (roomTypeId && roomsByType[roomTypeId]) {
                roomsByType[roomTypeId].forEach(room => {
                    const option = document.createElement('option');
                    option.value = room.RoomID;
                    option.textContent = room.RoomNumber;
                    roomSelect.appendChild(option);
                });
            }

            calculateTotal();
        }

        function calculateTotal() {
            const roomTypeId = document.getElementById('room_type_id').value;
            const checkinDate = document.querySelector('input[name="checkin_date"]').value;
            const checkoutDate = document.querySelector('input[name="checkout_date"]').value;
            const rateType = document.getElementById('rate_type').value;

            if (!roomTypeId || !checkinDate || !checkoutDate) {
                document.getElementById('total_amount').value = '';
                return;
            }

            const checkin = new Date(checkinDate);
            const checkout = new Date(checkoutDate);
            const nights = Math.ceil((checkout - checkin) / (1000 * 60 * 60 * 24));

            if (nights <= 0) {
                document.getElementById('total_amount').value = 'Invalid dates';
                return;
            }

            const rates = roomTypes[roomTypeId];
            let total = 0;

            if (rateType === 'Weekly') {
                const weeks = Math.ceil(nights / 7);
                total = weeks * rates.WeeklyRate;
            } else if (rateType === 'Monthly') {
                const months = Math.ceil(nights / 30);
                total = months * rates.MonthlyRate;
            } else {
                total = nights * rates.DailyRate;
            }

            document.getElementById('total_amount').value = 'Rs. ' + total.toLocaleString() + ' (' + nights + ' nights)';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotal();
        });
    </script>
</body>
</html>
