<?php
// Get current page name for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
?>
<div class="sidebar">
    <div class="sidebar-header">
        <h3><i class="fa fa-hotel"></i> Echo Hotels</h3>
        <p>Reservation Clerk</p>
    </div>

    <ul class="sidebar-menu">
        <li class="<?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
            <a href="dashboard.php">
                <i class="fa fa-dashboard"></i> Dashboard
            </a>
        </li>

        <li class="<?php echo $current_page === 'make_reservation.php' ? 'active' : ''; ?>">
            <a href="make_reservation.php">
                <i class="fa fa-plus-square"></i> Make Reservation
            </a>
        </li>

        <li class="<?php echo $current_page === 'reservations.php' ? 'active' : ''; ?>">
            <a href="reservations.php">
                <i class="fa fa-calendar"></i> View Reservations
            </a>
        </li>

        <li class="<?php echo $current_page === 'checkin.php' ? 'active' : ''; ?>">
            <a href="checkin.php">
                <i class="fa fa-sign-in"></i> Check-In
            </a>
        </li>

        <li class="<?php echo $current_page === 'checkout.php' ? 'active' : ''; ?>">
            <a href="checkout.php">
                <i class="fa fa-sign-out"></i> Check-Out
            </a>
        </li>

        <li class="<?php echo $current_page === 'walk_in.php' ? 'active' : ''; ?>">
            <a href="walk_in.php">
                <i class="fa fa-user-plus"></i> Walk-In Guest
            </a>
        </li>

        <li class="<?php echo $current_page === 'test_walkin_data.php' ? 'active' : ''; ?>">
            <a href="test_walkin_data.php">
                <i class="fa fa-database"></i> Walk-In Data
            </a>
        </li>

        <li class="<?php echo $current_page === 'room_status.php' ? 'active' : ''; ?>">
            <a href="room_status.php">
                <i class="fa fa-bed"></i> Room Status
            </a>
        </li>

        <li class="<?php echo $current_page === 'billing.php' ? 'active' : ''; ?>">
            <a href="billing.php">
                <i class="fa fa-money"></i> Billing
            </a>
        </li>

        <li class="<?php echo $current_page === 'additional_charges.php' ? 'active' : ''; ?>">
            <a href="additional_charges.php">
                <i class="fa fa-plus-circle"></i> Additional Charges
            </a>
        </li>

        <li class="<?php echo $current_page === 'reports.php' ? 'active' : ''; ?>">
            <a href="reports.php">
                <i class="fa fa-bar-chart"></i> Reports
            </a>
        </li>
    </ul>

    <div class="sidebar-footer">
        <div class="user-info">
            <i class="fa fa-user"></i>
            <span><?php echo htmlspecialchars($_SESSION['clerk_name']); ?></span>
        </div>
        <a href="logout.php" class="btn btn-danger btn-sm">
            <i class="fa fa-sign-out"></i> Logout
        </a>
    </div>
</div>
