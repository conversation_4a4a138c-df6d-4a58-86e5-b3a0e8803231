<?php
session_start();
require_once('../config/db.php');

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $message = 'Please fill in all fields';
        $message_type = 'error';
    } else {
        // Check employee credentials
        $query = "SELECT EmployeeID, FirstName, LastName, Email, Role, HotelID, PasswordHash FROM Employees WHERE Email = ? AND Role = 'ReservationClerk'";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $employee = $result->fetch_assoc();
            
            if (password_verify($password, $employee['PasswordHash'])) {
                $_SESSION['clerk_id'] = $employee['EmployeeID'];
                $_SESSION['clerk_name'] = $employee['FirstName'] . ' ' . $employee['LastName'];
                $_SESSION['clerk_email'] = $employee['Email'];
                $_SESSION['clerk_hotel_id'] = $employee['HotelID'];
                $_SESSION['role'] = 'ReservationClerk';
                
                header('Location: dashboard.php');
                exit();
            } else {
                $message = 'Invalid email or password';
                $message_type = 'error';
            }
        } else {
            $message = 'Invalid email or password';
            $message_type = 'error';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservation Clerk Login - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h2 {
            color: #333;
            font-weight: bold;
        }
        .form-group label {
            font-weight: 600;
            color: #555;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 600;
        }
        .alert {
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h2><i class="fa fa-hotel"></i> Echo Hotels</h2>
            <p class="text-muted">Reservation Clerk Portal</p>
        </div>
        
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" class="form-control" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn btn-primary btn-block btn-login">
                <i class="fa fa-sign-in"></i> Login
            </button>
        </form>
        
        <div class="text-center mt-3">
            <a href="../Manager/login.php" class="text-muted">Manager Login</a> |
            <a href="../index.php" class="text-muted">Back to Website</a>
        </div>
    </div>
    
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
</body>
</html>
