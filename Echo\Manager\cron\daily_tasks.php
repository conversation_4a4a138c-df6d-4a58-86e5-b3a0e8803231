<?php
require_once('../../config/db.php');

// Set timezone
date_default_timezone_set('Asia/Colombo');

// Log function
function log_message($message) {
    $log_file = __DIR__ . '/daily_tasks.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND);
}

log_message('Starting daily tasks...');

// 1. Auto-cancel reservations without credit card details (12 hours after creation)
$res_query = "
    SELECT r.*,
           CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
           c.Email
    FROM Reservations r
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    WHERE r.Status = 'Confirmed'
    AND (r.CreditCardDetails IS NULL OR r.CreditCardDetails = '')
    AND TIMESTAMPDIFF(HOUR, r.CreatedAt, NOW()) >= 12
";

$res_result = $conn->query($res_query);
$cancelled_count = 0;

while ($reservation = $res_result->fetch_assoc()) {
    // Update reservation status
    $update = "UPDATE Reservations SET Status = 'Canceled', UpdatedAt = NOW() WHERE ReservationID = ?";
    $stmt = $conn->prepare($update);
    $stmt->bind_param("i", $reservation['ReservationID']);

    if ($stmt->execute()) {
        $cancelled_count++;
        log_message("Cancelled reservation #{$reservation['ReservationID']} for {$reservation['CustomerName']} - No credit card details");

        // Here you would send an email to the customer
        // Implementation of email sending would go here
    }
}

// 1b. Auto-cancel reservations scheduled for 7 PM cancellation
$evening_cancel_query = "
    UPDATE Reservations
    SET Status = 'Canceled', UpdatedAt = NOW()
    WHERE Status = 'Confirmed'
    AND (CreditCardDetails IS NULL OR CreditCardDetails = '')
    AND AutoCancelAt IS NOT NULL
    AND AutoCancelAt <= NOW()
";

$evening_result = $conn->query($evening_cancel_query);
$evening_cancelled = $evening_result ? $conn->affected_rows : 0;

log_message("Auto-cancelled $cancelled_count reservations without credit card details (12h rule)");
log_message("Auto-cancelled $evening_cancelled reservations at 7 PM daily check");

// 2. Handle no-shows and create billing records
$no_shows_query = "
    SELECT r.*,
           rt.DailyRate,
           CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
           c.Email
    FROM Reservations r
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    JOIN Customers c ON r.CustomerID = c.CustomerID
    WHERE r.Status = 'Confirmed'
    AND r.StartDate < CURDATE()
    AND NOT EXISTS (
        SELECT 1 FROM CheckIns ci
        WHERE ci.ReservationID = r.ReservationID
    )
";

$no_shows_result = $conn->query($no_shows_query);
$no_show_count = 0;
$total_no_show_revenue = 0;

while ($no_show = $no_shows_result->fetch_assoc()) {
    // Create billing record
    $billing_query = "
        INSERT INTO Billing (
            ReservationID,
            TotalAmount,
            PaymentMethod,
            PaymentStatus,
            BillingDateTime
        ) VALUES (?, ?, 'CreditCard', 'Pending', NOW())
    ";

    $stmt = $conn->prepare($billing_query);
    $total_amount = 1500.00; // Fixed no-show charge as per hotel policy
    $stmt->bind_param("id", $no_show['ReservationID'], $total_amount);

    if ($stmt->execute()) {
        // Update reservation status
        $update = "UPDATE Reservations SET Status = 'NoShow' WHERE ReservationID = ?";
        $stmt = $conn->prepare($update);
        $stmt->bind_param("i", $no_show['ReservationID']);
        $stmt->execute();

        $no_show_count++;
        $total_no_show_revenue += $total_amount;
        log_message("Created no-show billing for reservation #{$no_show['ReservationID']} - {$no_show['CustomerName']} - Amount: " . format_currency($total_amount));

        // Here you would charge the customer's credit card
        // Implementation of payment processing would go here
    }
}

log_message("Processed $no_show_count no-shows with total revenue of " . format_currency($total_no_show_revenue));

// 3. Generate occupancy report
$report = get_occupancy_report();
$report_data = [];
$total_occupancy = 0;
$total_revenue = 0;

while ($row = $report->fetch_assoc()) {
    $occupancy_rate = ($row['TotalRooms'] > 0)
        ? round(($row['OccupiedRooms'] / $row['TotalRooms']) * 100, 2)
        : 0;

    $report_data[] = [
        'hotel' => $row['HotelName'],
        'total_rooms' => $row['TotalRooms'],
        'occupied_rooms' => $row['OccupiedRooms'],
        'occupancy_rate' => $occupancy_rate,
        'revenue' => $row['Revenue']
    ];

    $total_occupancy += $row['OccupiedRooms'];
    $total_revenue += $row['Revenue'];
}

// Generate report content
$report_content = "Daily Occupancy and Revenue Report - " . date('Y-m-d', strtotime('-1 day')) . "\n\n";
$report_content .= str_repeat('=', 80) . "\n";

foreach ($report_data as $data) {
    $report_content .= "\n{$data['hotel']}\n";
    $report_content .= str_repeat('-', 40) . "\n";
    $report_content .= "Total Rooms: {$data['total_rooms']}\n";
    $report_content .= "Occupied Rooms: {$data['occupied_rooms']}\n";
    $report_content .= "Occupancy Rate: {$data['occupancy_rate']}%\n";
    $report_content .= "Revenue: " . format_currency($data['revenue']) . "\n";
}

$report_content .= "\n" . str_repeat('=', 80) . "\n";
$report_content .= "\nSummary:\n";
$report_content .= "Total Occupied Rooms: $total_occupancy\n";
$report_content .= "Total Revenue: " . format_currency($total_revenue) . "\n";

// Save report to file
$report_file = __DIR__ . '/reports/occupancy_' . date('Y-m-d') . '.txt';
if (!is_dir(__DIR__ . '/reports')) {
    mkdir(__DIR__ . '/reports', 0755, true);
}
file_put_contents($report_file, $report_content);

log_message("Generated occupancy report: $report_file");
log_message('Daily tasks completed successfully');

// Close database connection
$conn->close();
?>