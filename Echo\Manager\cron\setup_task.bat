@echo off
echo Setting up Echo Hotel Management System daily tasks...

:: Get the current directory
set SCRIPT_PATH=%~dp0daily_tasks.php
set PHP_PATH=C:\xampp\php\php.exe

:: Create the scheduled task
schtasks /create /tn "Echo Hotel Daily Tasks" /tr "%PHP_PATH% %SCRIPT_PATH%" /sc daily /st 19:00 /ru SYSTEM /f

if %errorlevel% equ 0 (
    echo Task scheduled successfully!
    echo The script will run daily at 7:00 PM
) else (
    echo Failed to schedule task. Please run as administrator.
)

pause 