<?php
session_start();
require_once('../../config/db.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['employee_id']) || $_SESSION['role'] !== 'Admin') {
    echo '<div class="alert alert-danger">Unauthorized access</div>';
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['company_id'])) {
    $company_id = intval($_POST['company_id']);

    try {
        // Get company reservations
        $query = "
            SELECT r.*,
                   CONCAT(c.FirstName, ' ', c.LastName) as CustomerName,
                   c.Email as CustomerEmail,
                   h.Name as HotelName,
                   h.Location as HotelLocation,
                   rt.TypeName as RoomType,
                   rt.DailyRate,
                   rm.RoomNumber,
                   DATEDIFF(r.EndDate, r.StartDate) as StayDuration,
                   (DATEDIFF(r.EndDate, r.StartDate) * rt.DailyRate) as TotalAmount
            FROM Reservations r
            LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
            JOIN Hotels h ON r.HotelID = h.HotelID
            JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
            LEFT JOIN CheckIns ci ON r.ReservationID = ci.ReservationID
            LEFT JOIN Rooms rm ON ci.RoomID = rm.RoomID
            WHERE r.CompanyID = ?
            ORDER BY r.CreatedAt DESC
        ";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $company_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            echo '<div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Customer</th>
                                <th>Hotel</th>
                                <th>Room Type</th>
                                <th>Room #</th>
                                <th>Check In</th>
                                <th>Check Out</th>
                                <th>Duration</th>
                                <th>Total</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>';

            while ($reservation = $result->fetch_assoc()) {
                // Status badge color
                $status_class = '';
                switch ($reservation['Status']) {
                    case 'Confirmed': $status_class = 'success'; break;
                    case 'CheckedIn': $status_class = 'info'; break;
                    case 'CheckedOut': $status_class = 'secondary'; break;
                    case 'Canceled': $status_class = 'danger'; break;
                    case 'NoShow': $status_class = 'dark'; break;
                    default: $status_class = 'warning';
                }

                echo '<tr>
                        <td>#' . htmlspecialchars($reservation['ReservationID']) . '</td>
                        <td>' . htmlspecialchars($reservation['CustomerName']) . '<br><small class="text-muted">' . htmlspecialchars($reservation['CustomerEmail']) . '</small></td>
                        <td>' . htmlspecialchars($reservation['HotelName']) . '<br><small class="text-muted">' . htmlspecialchars($reservation['HotelLocation']) . '</small></td>
                        <td>' . htmlspecialchars($reservation['RoomType']) . '</td>
                        <td>' . htmlspecialchars($reservation['RoomNumber'] ?: 'Not assigned') . '</td>
                        <td>' . date('M j, Y', strtotime($reservation['StartDate'])) . '</td>
                        <td>' . date('M j, Y', strtotime($reservation['EndDate'])) . '</td>
                        <td>' . $reservation['StayDuration'] . ' night(s)</td>
                        <td>' . format_currency($reservation['TotalAmount']) . '</td>
                        <td><span class="badge badge-' . $status_class . '">' . htmlspecialchars($reservation['Status']) . '</span></td>
                      </tr>';
            }

            echo '</tbody></table></div>';

            // Summary statistics
            $stats_query = "
                SELECT
                    COUNT(*) as TotalReservations,
                    SUM(CASE WHEN Status = 'Confirmed' THEN 1 ELSE 0 END) as ConfirmedReservations,
                    SUM(CASE WHEN Status = 'CheckedOut' THEN 1 ELSE 0 END) as CompletedStays,
                    SUM(CASE WHEN Status = 'Canceled' THEN 1 ELSE 0 END) as CanceledReservations,
                    SUM(CASE WHEN Status = 'CheckedOut' THEN (DATEDIFF(EndDate, StartDate) * (SELECT DailyRate FROM RoomTypes WHERE RoomTypeID = r.RoomTypeID)) ELSE 0 END) as TotalRevenue
                FROM Reservations r
                WHERE CompanyID = ?
            ";

            $stats_stmt = $conn->prepare($stats_query);
            $stats_stmt->bind_param("i", $company_id);
            $stats_stmt->execute();
            $stats_result = $stats_stmt->get_result();
            $stats = $stats_result->fetch_assoc();

            echo '<hr>
                  <div class="row">
                      <div class="col-md-3">
                          <div class="card bg-primary text-white">
                              <div class="card-body text-center">
                                  <h4>' . $stats['TotalReservations'] . '</h4>
                                  <small>Total Reservations</small>
                              </div>
                          </div>
                      </div>
                      <div class="col-md-3">
                          <div class="card bg-success text-white">
                              <div class="card-body text-center">
                                  <h4>' . $stats['CompletedStays'] . '</h4>
                                  <small>Completed Stays</small>
                              </div>
                          </div>
                      </div>
                      <div class="col-md-3">
                          <div class="card bg-warning text-white">
                              <div class="card-body text-center">
                                  <h4>' . $stats['ConfirmedReservations'] . '</h4>
                                  <small>Confirmed</small>
                              </div>
                          </div>
                      </div>
                      <div class="col-md-3">
                          <div class="card bg-info text-white">
                              <div class="card-body text-center">
                                  <h4>' . format_currency($stats['TotalRevenue']) . '</h4>
                                  <small>Total Revenue</small>
                              </div>
                          </div>
                      </div>
                  </div>';

        } else {
            echo '<div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> This company has no reservations yet.
                  </div>';
        }

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error loading company reservations: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
} else {
    echo '<div class="alert alert-danger">Invalid request.</div>';
}

$conn->close();
?>
