# Walk-In Guest Functionality Documentation

## Overview
The Walk-In Guest functionality in the Echo Hotels Clerk Dashboard allows reservation clerks to register and immediately check-in guests who arrive without prior reservations.

## What Happens When a Walk-In Guest is Registered

### 1. Customer Record Creation
- **Always creates a customer record** in the `Customers` table
- If email is provided and customer exists: Updates existing customer details
- If email is provided and customer doesn't exist: Creates new customer with email
- If no email provided: Creates new customer without email (NULL email)
- Default password: `walkin123` (can be changed later)

### 2. Reservation Creation
- Creates a reservation record in the `Reservations` table
- Status: Initially `Confirmed`, then updated to `CheckedIn`
- Links to the customer and selected room type
- Includes rate type (Daily/Weekly/Monthly) and pricing

### 3. Immediate Check-In
- Creates a check-in record in the `CheckIns` table
- Links reservation, customer, hotel, and specific room
- Sets check-in time to current timestamp
- Records expected checkout date

### 4. Room Management
- Updates room status to `Occupied` in the `Rooms` table
- Prevents double-booking of the same room

### 5. Billing Setup
- Creates initial billing record in the `Billing` table
- Payment method: `Cash` (default for walk-ins)
- Payment status: `Pending`
- Calculates total amount based on rate type and duration

## Database Tables Affected

### Primary Tables:
1. **Customers** - Guest personal information
2. **CheckIns** - Check-in records with timestamps
3. **Reservations** - Booking details and status
4. **Rooms** - Room availability status
5. **Billing** - Payment and billing information

## Form Fields

### Required Fields:
- First Name
- Last Name
- Room Type
- Room Number
- Check-in Date
- Check-out Date
- Number of Guests

### Optional Fields:
- Email (recommended for future bookings)
- Phone
- Special Requests/Notes

## Features

### Automatic Calculations:
- Total amount based on rate type (Daily/Weekly/Monthly)
- Number of nights calculation
- Real-time price updates

### Smart Customer Handling:
- Checks for existing customers by email
- Updates existing customer information if found
- Creates new customer if not found or no email provided

### Transaction Safety:
- Uses database transactions for data integrity
- Rolls back all changes if any step fails
- Ensures consistent data across all tables

## Access and Verification

### Walk-In Registration:
- Navigate to: `Clerk Dashboard > Walk-In Guest`
- URL: `/Echo/Clerk/walk_in.php`

### Data Verification:
- Navigate to: `Clerk Dashboard > Walk-In Data`
- URL: `/Echo/Clerk/test_walkin_data.php`
- Shows recent walk-in guests and their records

## Success Confirmation

After successful registration, the system displays:
- Customer ID (newly created)
- Reservation ID
- Check-in ID
- Guest name and room assignment
- Total billing amount

## Error Handling

The system handles various error scenarios:
- Room availability conflicts
- Database connection issues
- Invalid date ranges
- Missing required information

All errors are logged and displayed to the clerk with appropriate messages.

## Integration with Other Systems

The walk-in guest data integrates seamlessly with:
- **Check-out process** - Uses check-in records
- **Billing system** - Links to billing records
- **Room management** - Updates room status
- **Reports** - Includes walk-in statistics
- **Additional charges** - Can add services to walk-in guests

## Best Practices

1. **Always collect contact information** when possible
2. **Verify room availability** before confirming
3. **Double-check dates** and guest count
4. **Use the verification page** to confirm data storage
5. **Process payment** immediately after registration

## Technical Notes

- Uses prepared statements for SQL security
- Implements proper error handling and rollback
- Follows the existing database schema
- Maintains referential integrity
- Supports all three hotel locations (Kandy, Jaffna, Galle)
