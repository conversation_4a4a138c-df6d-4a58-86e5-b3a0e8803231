<?php
/**
 * Database migration script to fix Reservations table schema
 * This script modifies the CustomerID column to allow NULL values for travel agent bookings
 */

require_once(__DIR__ . '/../config/db.php');

echo "Starting database migration to fix Reservations table schema...\n";

try {
    // Check current schema
    $check_query = "DESCRIBE Reservations";
    $result = $conn->query($check_query);
    
    echo "Current Reservations table schema:\n";
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']} {$row['Null']} {$row['Key']} {$row['Default']}\n";
    }
    
    // Modify CustomerID to allow NULL values
    echo "\nModifying CustomerID column to allow NULL values...\n";
    
    $alter_query = "ALTER TABLE Reservations MODIFY COLUMN CustomerID int(11) NULL";
    
    if ($conn->query($alter_query) === TRUE) {
        echo "✓ CustomerID column modified successfully to allow NULL values.\n";
    } else {
        throw new Exception("Error modifying CustomerID column: " . $conn->error);
    }
    
    // Add missing columns if they don't exist
    $columns_to_add = [
        'RateType' => "ALTER TABLE Reservations ADD COLUMN RateType enum('Daily','Weekly','Monthly') DEFAULT 'Daily' AFTER DiscountedRate",
        'CancellationReason' => "ALTER TABLE Reservations ADD COLUMN CancellationReason TEXT NULL AFTER Status"
    ];
    
    foreach ($columns_to_add as $column => $query) {
        // Check if column exists
        $check_column = "SHOW COLUMNS FROM Reservations LIKE '$column'";
        $result = $conn->query($check_column);
        
        if ($result->num_rows == 0) {
            echo "Adding $column column...\n";
            if ($conn->query($query) === TRUE) {
                echo "✓ $column column added successfully.\n";
            } else {
                echo "⚠ Warning: Could not add $column column: " . $conn->error . "\n";
            }
        } else {
            echo "✓ $column column already exists.\n";
        }
    }
    
    // Update Status enum to include more options
    echo "\nUpdating Status enum values...\n";
    $update_status_query = "ALTER TABLE Reservations MODIFY COLUMN Status enum('Confirmed','Cancelled','NoShow','No-Show') DEFAULT 'Confirmed'";
    
    if ($conn->query($update_status_query) === TRUE) {
        echo "✓ Status enum updated successfully.\n";
    } else {
        echo "⚠ Warning: Could not update Status enum: " . $conn->error . "\n";
    }
    
    // Update Billing table PaymentMethod enum
    echo "\nUpdating Billing table PaymentMethod enum...\n";
    $update_billing_query = "ALTER TABLE Billing MODIFY COLUMN PaymentMethod enum('Cash','CreditCard','Credit Card','Company Billing','No-Show Charge') NOT NULL";
    
    if ($conn->query($update_billing_query) === TRUE) {
        echo "✓ Billing PaymentMethod enum updated successfully.\n";
    } else {
        echo "⚠ Warning: Could not update Billing PaymentMethod enum: " . $conn->error . "\n";
    }
    
    // Update Billing table PaymentStatus enum
    echo "\nUpdating Billing table PaymentStatus enum...\n";
    $update_billing_status_query = "ALTER TABLE Billing MODIFY COLUMN PaymentStatus enum('Pending','Paid','No-Show') DEFAULT 'Pending'";
    
    if ($conn->query($update_billing_status_query) === TRUE) {
        echo "✓ Billing PaymentStatus enum updated successfully.\n";
    } else {
        echo "⚠ Warning: Could not update Billing PaymentStatus enum: " . $conn->error . "\n";
    }
    
    // Show final schema
    echo "\nFinal Reservations table schema:\n";
    $final_check = $conn->query("DESCRIBE Reservations");
    while ($row = $final_check->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']} {$row['Null']} {$row['Key']} {$row['Default']}\n";
    }
    
    echo "\n✅ Database migration completed successfully!\n";
    echo "\nThe Reservations table now supports:\n";
    echo "- Travel agent bookings (CustomerID can be NULL, CompanyID used instead)\n";
    echo "- Customer bookings (CustomerID required, CompanyID is NULL)\n";
    echo "- Enhanced status tracking\n";
    echo "- Improved billing options\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

$conn->close();
?>
