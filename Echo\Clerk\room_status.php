<?php
session_start();
require_once('../config/db.php');

// Check if user is logged in and is a reservation clerk
if (!isset($_SESSION['clerk_id']) || $_SESSION['role'] !== 'ReservationClerk') {
    header('Location: login.php');
    exit();
}

$clerk_hotel_id = $_SESSION['clerk_hotel_id'];
$message = '';
$message_type = '';

// Handle room status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $room_id = $_POST['room_id'];
    $new_status = $_POST['new_status'];
    
    try {
        $update_query = "UPDATE Rooms SET Status = ? WHERE RoomID = ? AND HotelID = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("sii", $new_status, $room_id, $clerk_hotel_id);
        
        if ($stmt->execute()) {
            $message = 'Room status updated successfully!';
            $message_type = 'success';
        } else {
            throw new Exception('Failed to update room status');
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// Get all rooms with their current status and occupancy information
$rooms_query = "
    SELECT r.RoomID, r.RoomNumber, r.Status,
           rt.TypeName as RoomType, rt.DailyRate,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as CurrentGuest,
           ci.CheckInDateTime,
           ci.ExpectedCheckOutDate,
           res.ReservationID
    FROM Rooms r
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    LEFT JOIN CheckIns ci ON r.RoomID = ci.RoomID AND ci.ActualCheckOutDateTime IS NULL
    LEFT JOIN Reservations res ON ci.ReservationID = res.ReservationID
    LEFT JOIN Customers c ON res.CustomerID = c.CustomerID
    WHERE r.HotelID = ?
    ORDER BY rt.TypeName, r.RoomNumber
";
$stmt = $conn->prepare($rooms_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$rooms_result = $stmt->get_result();

// Get upcoming arrivals for today
$today = date('Y-m-d');
$arrivals_query = "
    SELECT r.ReservationID, r.StartDate,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as GuestName,
           rt.TypeName as RoomType,
           r.NumberOfGuests
    FROM Reservations r
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.HotelID = ? AND r.StartDate = ? AND r.Status = 'Confirmed'
    AND NOT EXISTS (SELECT 1 FROM CheckIns ci WHERE ci.ReservationID = r.ReservationID)
    ORDER BY r.StartDate
";
$stmt = $conn->prepare($arrivals_query);
$stmt->bind_param("is", $clerk_hotel_id, $today);
$stmt->execute();
$arrivals_result = $stmt->get_result();

// Get departures for today
$departures_query = "
    SELECT ci.CheckInID, ci.ExpectedCheckOutDate,
           CONCAT(COALESCE(c.FirstName, 'Walk-in'), ' ', COALESCE(c.LastName, 'Guest')) as GuestName,
           rm.RoomNumber,
           rt.TypeName as RoomType
    FROM CheckIns ci
    JOIN Reservations r ON ci.ReservationID = r.ReservationID
    LEFT JOIN Customers c ON r.CustomerID = c.CustomerID
    JOIN Rooms rm ON ci.RoomID = rm.RoomID
    JOIN RoomTypes rt ON r.RoomTypeID = rt.RoomTypeID
    WHERE r.HotelID = ? AND ci.ExpectedCheckOutDate = ? AND ci.ActualCheckOutDateTime IS NULL
    ORDER BY rm.RoomNumber
";
$stmt = $conn->prepare($departures_query);
$stmt->bind_param("is", $clerk_hotel_id, $today);
$stmt->execute();
$departures_result = $stmt->get_result();

// Get room statistics
$stats_query = "
    SELECT 
        COUNT(*) as TotalRooms,
        SUM(CASE WHEN Status = 'Available' THEN 1 ELSE 0 END) as AvailableRooms,
        SUM(CASE WHEN Status = 'Occupied' THEN 1 ELSE 0 END) as OccupiedRooms,
        SUM(CASE WHEN Status = 'Maintenance' THEN 1 ELSE 0 END) as MaintenanceRooms
    FROM Rooms 
    WHERE HotelID = ?
";
$stmt = $conn->prepare($stats_query);
$stmt->bind_param("i", $clerk_hotel_id);
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Room Status - Echo Hotels</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="css/clerk.css">
    <style>
        .room-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .room-card.available {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .room-card.occupied {
            border-color: #dc3545;
            background-color: #fff8f8;
        }
        .room-card.maintenance {
            border-color: #ffc107;
            background-color: #fffdf5;
        }
        .room-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .room-type {
            color: #666;
            font-size: 0.9rem;
        }
        .guest-info {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="clerk-wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="header">
                <h1>Room Status Overview</h1>
                <div class="user-info">
                    Welcome, <?php echo htmlspecialchars($_SESSION['clerk_name']); ?>
                </div>
            </div>
            
            <div class="content">
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                <?php endif; ?>

                <!-- Room Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card primary">
                            <h3><?php echo $stats['TotalRooms']; ?></h3>
                            <p>Total Rooms</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card success">
                            <h3><?php echo $stats['AvailableRooms']; ?></h3>
                            <p>Available</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card warning">
                            <h3><?php echo $stats['OccupiedRooms']; ?></h3>
                            <p>Occupied</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card info">
                            <h3><?php echo $stats['MaintenanceRooms']; ?></h3>
                            <p>Maintenance</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Room Status Grid -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>All Rooms</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php while ($room = $rooms_result->fetch_assoc()): ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="room-card <?php echo strtolower($room['Status']); ?>">
                                            <div class="room-number">
                                                <?php echo htmlspecialchars($room['RoomNumber']); ?>
                                                <span class="badge badge-<?php 
                                                    echo $room['Status'] === 'Available' ? 'success' : 
                                                        ($room['Status'] === 'Occupied' ? 'danger' : 'warning'); 
                                                ?>">
                                                    <?php echo $room['Status']; ?>
                                                </span>
                                            </div>
                                            <div class="room-type">
                                                <?php echo htmlspecialchars($room['RoomType']); ?> - 
                                                Rs. <?php echo number_format($room['DailyRate']); ?>/night
                                            </div>
                                            
                                            <?php if ($room['CurrentGuest']): ?>
                                            <div class="guest-info">
                                                <strong>Guest:</strong> <?php echo htmlspecialchars($room['CurrentGuest']); ?><br>
                                                <strong>Check-in:</strong> <?php echo date('M d, H:i', strtotime($room['CheckInDateTime'])); ?><br>
                                                <strong>Expected Check-out:</strong> <?php echo date('M d', strtotime($room['ExpectedCheckOutDate'])); ?>
                                            </div>
                                            <?php endif; ?>
                                            
                                            <div class="mt-2">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="changeRoomStatus(<?php echo $room['RoomID']; ?>, '<?php echo $room['RoomNumber']; ?>', '<?php echo $room['Status']; ?>')">
                                                    Change Status
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endwhile; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Today's Activities -->
                    <div class="col-md-4">
                        <!-- Expected Arrivals -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5>Today's Arrivals</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($arrivals_result->num_rows > 0): ?>
                                    <?php while ($arrival = $arrivals_result->fetch_assoc()): ?>
                                    <div class="mb-2 p-2 border-left border-primary">
                                        <strong><?php echo htmlspecialchars($arrival['GuestName']); ?></strong><br>
                                        <small><?php echo htmlspecialchars($arrival['RoomType']); ?> - <?php echo $arrival['NumberOfGuests']; ?> guests</small>
                                    </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <p class="text-muted">No arrivals expected today</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Expected Departures -->
                        <div class="card">
                            <div class="card-header">
                                <h5>Today's Departures</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($departures_result->num_rows > 0): ?>
                                    <?php while ($departure = $departures_result->fetch_assoc()): ?>
                                    <div class="mb-2 p-2 border-left border-warning">
                                        <strong><?php echo htmlspecialchars($departure['GuestName']); ?></strong><br>
                                        <small>Room <?php echo $departure['RoomNumber']; ?> - <?php echo htmlspecialchars($departure['RoomType']); ?></small>
                                    </div>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <p class="text-muted">No departures expected today</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Room Status Modal -->
    <div class="modal fade" id="changeStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Room Status</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="update_status" value="1">
                        <input type="hidden" id="room_id" name="room_id">
                        
                        <div class="form-group">
                            <label>Room Number</label>
                            <input type="text" id="room_number" class="form-control" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label>Current Status</label>
                            <input type="text" id="current_status" class="form-control" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label>New Status *</label>
                            <select name="new_status" class="form-control" required>
                                <option value="">Select new status</option>
                                <option value="Available">Available</option>
                                <option value="Occupied">Occupied</option>
                                <option value="Maintenance">Maintenance</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script>
        function changeRoomStatus(roomId, roomNumber, currentStatus) {
            document.getElementById('room_id').value = roomId;
            document.getElementById('room_number').value = roomNumber;
            document.getElementById('current_status').value = currentStatus;
            
            $('#changeStatusModal').modal('show');
        }
    </script>
</body>
</html>
