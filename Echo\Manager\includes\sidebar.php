<?php
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>
<div class="sidebar">
    <div class="logo">
        <a href="../index.html">
            <img src="../img/logo.png" alt="Echo Hotels">
        </a>
    </div>
    <ul class="nav-menu">
        <li class="nav-item <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>">
            <a href="dashboard.php">
                <i class="fa fa-dashboard"></i>
                <span>Dashboard</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'hotels' ? 'active' : ''; ?>">
            <a href="hotels.php">
                <i class="fa fa-building"></i>
                <span>Hotels</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'rooms' ? 'active' : ''; ?>">
            <a href="rooms.php">
                <i class="fa fa-bed"></i>
                <span>Rooms</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'reservations' ? 'active' : ''; ?>">
            <a href="reservations.php">
                <i class="fa fa-calendar"></i>
                <span>Reservations</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'customers' ? 'active' : ''; ?>">
            <a href="customers.php">
                <i class="fa fa-users"></i>
                <span>Customers</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'employees' ? 'active' : ''; ?>">
            <a href="employees.php">
                <i class="fa fa-user-circle"></i>
                <span>Employees</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'companies' ? 'active' : ''; ?>">
            <a href="companies.php">
                <i class="fa fa-building-o"></i>
                <span>Travel Companies</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'billing' ? 'active' : ''; ?>">
            <a href="billing.php">
                <i class="fa fa-credit-card"></i>
                <span>Billing</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'reports' ? 'active' : ''; ?>">
            <a href="reports.php">
                <i class="fa fa-line-chart"></i>
                <span>Reports</span>
            </a>
        </li>
        <li class="nav-item <?php echo $current_page === 'settings' ? 'active' : ''; ?>">
            <a href="settings.php">
                <i class="fa fa-cog"></i>
                <span>Settings</span>
            </a>
        </li>
    </ul>
    <div class="sidebar-footer">
        <div class="user-info">
            <img src="../img/admin-avatar.png" alt="Admin" class="user-avatar">
            <div class="user-details">
                <span class="user-name"><?php echo htmlspecialchars($_SESSION['employee_name'] ?? 'Admin'); ?></span>
                <span class="user-role"><?php echo htmlspecialchars($_SESSION['role'] ?? 'Administrator'); ?></span>
            </div>
        </div>
        <a href="logout.php" class="logout-btn">
            <i class="fa fa-sign-out"></i>
            <span>Logout</span>
        </a>
    </div>
</div>

<style>
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow: hidden;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    top: 0;
    left: 0;
}

.sidebar .logo {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar .logo img {
    max-width: 150px;
    height: auto;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
    overflow-y: auto;
}

.nav-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item a {
    display: block;
    padding: 10px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.nav-item a:hover,
.nav-item.active a {
    background-color: rgba(255, 255, 255, 0.1);
    padding-left: 30px;
    color: white;
}

.nav-item i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.nav-item.active i,
.nav-item a:hover i {
    color: white;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-details {
    flex-grow: 1;
}

.user-name {
    display: block;
    font-size: 14px;
    font-weight: bold;
    color: white;
}

.user-role {
    display: block;
    font-size: 12px;
    color: rgba(255,255,255,0.7);
}

.logout-btn {
    width: 100%;
    border-radius: 8px;
    font-weight: 600;
    padding: 8px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    text-align: center;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    color: white;
}

.logout-btn i {
    margin-right: 8px;
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .sidebar.show {
        transform: translateX(0);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add mobile menu toggle functionality
    const menuToggle = document.createElement('button');
    menuToggle.className = 'menu-toggle';
    menuToggle.innerHTML = '<i class="fa fa-bars"></i>';
    document.querySelector('.header').prepend(menuToggle);

    menuToggle.addEventListener('click', function() {
        document.querySelector('.sidebar').classList.toggle('show');
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 992) {
            const sidebar = document.querySelector('.sidebar');
            const menuToggle = document.querySelector('.menu-toggle');

            if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        }
    });
});
</script>